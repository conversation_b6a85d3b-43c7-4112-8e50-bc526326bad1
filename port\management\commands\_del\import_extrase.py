from django.core.management.base import BaseCommand, CommandError
from django.conf import settings 
from port.models import Instrument, Ubo, Partner, Operation, Journal, Custodian, Account, Portfolio
import glob
import os
import pandas as pd
import datetime

# COMDANDA DEZAFECTATA, IMPLEMENTATA DIRECT IN JURNAL/ ADMIN

class Command(BaseCommand):

    def handle(self, *args, **options):

        UBO = 'DD'
        YEAR = datetime.datetime.today().year
        MONTH = datetime.datetime.today().month - 1

        # Import tabele - an curent lina curenta
        root = os.path.join(settings.FILE_ROOT, 'statements', str(YEAR), str(MONTH).zfill(2))
        list_of_files = glob.glob(os.path.join(root, '*.xls*')) 

        # Incarcare operatii
        operatii = [x for x in list_of_files if 'opera' in x][0]
        df_op = pd.read_excel(operatii) #.dropna(axis=1, how='all')
        df_op.columns =  df_op.columns.str.lower()

        # Incarcare solduri
        solduri = [x for x in list_of_files if 'sold' in x][0]
        df_sold = pd.read_excel(solduri) #.dropna(axis=1, how='all')
        df_sold.columns =  df_sold.columns.str.lower()

        # Upload operatii to database 
        model_instances = [
            Journal(
                ubo = Ubo.objects.get(ubo_code = row['ubo']),
                custodian = Custodian.objects.get(custodian_code = row['custodian']),
                account = Account.objects.get(account_code = row['account']),
                operation = Operation.objects.get(operation_code = '__DE_CLARIFICAT'),
                partner = Partner.objects.get(partner_code = row['partner']),
                instrument = Instrument.objects.get(symbol=row['symbol'], custodian__custodian_code=row['custodian']),
                date = row['date'], 
                transactionid = row['transactionid'], 
                value = row['value'], 
                quantity = row['quantity'], 
                details = row['details'], 
                storno = False, 
                lock = False,
            )
            for i, row in df_op.iterrows()]
        unique = ['ubo', 'custodian', 'account', 'transactionid', ]
        update_fields = unique + ['partner', 'instrument', 'date', 'value', 'quantity', 'details', 'storno', 'lock', 'operation', ] 
        Journal.objects.bulk_create(
            model_instances, 
            update_conflicts=False,
            ignore_conflicts=True,
            unique_fields=unique,
            update_fields=update_fields,
        )
        
        # Upload solduri to database 
        model_instances = [
            Portfolio(
                ubo = Ubo.objects.get(ubo_code = row['ubo']),
                instrument = Instrument.objects.get(symbol=row['symbol'], custodian__custodian_code=row['custodian']),
                date = row['date'], 
                cost = 1,
                value = row['value'], 
                quantity = -row['value'], 
                accruedint = 0
            )
            for i, row in df_sold.iterrows()]
        unique = ['ubo', 'instrument', 'date', ]
        update_fields = unique + ['cost', 'value', 'quantity', 'accruedint', ]
        Portfolio.objects.bulk_create(
            model_instances, 
            update_conflicts=False,
            ignore_conflicts=True,
            unique_fields=unique,
            update_fields=update_fields,
        )

        print('IMPORTED')
