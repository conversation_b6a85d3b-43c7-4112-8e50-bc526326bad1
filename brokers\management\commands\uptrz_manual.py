""" Export excel transactions"""
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings 
from port.models import Instrument, Currency, Ubo, Partner, Operation, Journal, Custodian, Account, Partner_type
import glob
import os
import pandas as pd


class Command(BaseCommand):

    def handle(self, *args, **options):

        # Import tabele
        tabs = [
            # 'journal_banks', 
            'journal_mst', 
            ]

        all = pd.DataFrame()

        root = settings.FILE_ROOT + 'reports/' 
        print(root)
        for t in tabs:
            list_of_files = glob.glob(root + t + '*.xlsx') 
            latest_file = max(list_of_files, key=os.path.getctime)
            df = pd.read_excel(latest_file) #.dropna(axis=1, how='all')
            all = pd.concat([all, df], ignore_index=True)


        # Define missing foreign keys
        # OPERATION
        # for x in all['operation'].unique():
        #     operationid = Operation.objects.filter(operation_code=x).all().values()
        #     if len(operationid)==0: # Insert operation
        #         obj = Operation(
        #             operation_code = x,
        #             operation_name = x,
        #         )
        #         obj.save()

        # ACCOUNTS
        # df = all.drop_duplicates(subset=['account'], keep='first')
        # unique_fields = ['account_code', ]
        # update_fields = unique_fields + ['ubo_code', 'custodian_code', 'currency_code', 'account_name',  ]
        # model_instances = [
        #     Account(
        #         currency_code = Currency.objects.get(currency_code = row['currency']),
        #         ubo_code = Ubo.objects.get(ubo_code = row['ubo']),
        #         custodian_code = Custodian.objects.get(custodian_code = row['custodian']),
        #         account_code = row['account'],
        #         account_name = row['account']
        #     )
        #     for i, row in df.iterrows()]
        # Account.objects.bulk_create(
        #     model_instances, 
        #     ignore_conflicts=True,
        #     unique_fields=unique_fields,
        #     update_fields=update_fields
        # )
 
        # PARTNERS
        # df = all.drop_duplicates(subset=['partner'], keep='first')
        # # print(df['custodian'].unique())
        # unique_fields = ['partner_code', ]
        # update_fields = unique_fields + ['partner_type', 'partner_name',  ]
        # model_instances = [
        #     Partner(
        #         partner_type = Partner_type.objects.get(partner_type_code = 'UNDEFINED',), 
        #         partner_code = row['partner'],
        #         partner_name = row['partner'],
        #     )
        #     for i, row in df.iterrows()]
        # Partner.objects.bulk_create(
        #     model_instances, 
        #     ignore_conflicts=True,
        #     unique_fields=unique_fields,
        #     update_fields=update_fields
        # )   


        # INSTRUMENTS
        # df = all.drop_duplicates(subset=['symbol', 'custodian'], keep='first')
        # unique_fields = ['symbol', 'custodian']
        # update_fields = unique_fields + ['currency', 'isin', 'name',  ]
        # model_instances = [
        #     Instrument(
        #         currency = Currency.objects.get(currency_code = row['currency']),
        #         custodian = Custodian.objects.get(custodian_code = row['custodian']),
        #         symbol = row['symbol'],
        #         isin = 'UNDEFINED',
        #         name = row['symbol'],
        #     )
        #     for i, row in df.iterrows()]
        # Instrument.objects.bulk_create(
        #     model_instances, 
        #     ignore_conflicts=True,
        #     unique_fields=unique_fields,
        #     update_fields=update_fields
        # )   


        # # Pregatire Calcul cost mediu ponderat
        # TRADES = ['BUY', 'SELL']
        # all_x = all[~(all['operation'].isin(TRADES))].copy()
        # trades = all[all['operation'].isin(TRADES)].copy()

        # # Calcul cost mediu ponderat
        # df = pd.DataFrame()
        # for symbol in trades['symbol'].unique():
        #     dx = trades[trades['symbol']==symbol].reset_index(drop=True).copy()
        #     dx[['value_at_cost','unit_cost']] = [0.0, 0.0 ]
        #     dx[['quantity', 'value']] = dx[['quantity', 'value']].astype(float)
            
        #     latest_cost = 0
        #     cumulated_value = 0
        #     cumulated_quantity = 0

        #     for index, row in dx.iterrows():
        #         if row['quantity'] >= 0:
        #             dx.loc[index, 'value_at_cost'] = -row['value']
        #             cumulated_value += -row['value']
        #             cumulated_quantity += row['quantity']
        #             latest_cost = cumulated_value / cumulated_quantity
        #             dx.loc[index, 'unit_cost'] = latest_cost
        #         else:
        #             cost = row['quantity'] * latest_cost
        #             dx.loc[index, 'value_at_cost'] = cost
        #             cumulated_quantity += row['quantity']
        #             cumulated_value += cost

        #             if cumulated_quantity > 0:
        #                 latest_cost = cumulated_value / cumulated_quantity
        #             else:
        #                 latest_cost = 0
        #             dx.loc[index, 'unit_cost'] = latest_cost
                    
        #     dx['profit'] = dx['value'] + dx['value_at_cost']
        #     df = pd.concat([df, dx], ignore_index=True)

        # df = df.drop(columns=['value_at_cost'])
        
        # all = pd.concat([df, all_x], ignore_index=True)
        # all['unit_cost'] = all['unit_cost'].fillna(0)
        # all['profit'] = all['profit'].fillna(0)

        # # Add profits
        # trades_profit = all[all['profit']!=0].copy()
        # if len(trades_profit)>0:
        #     trades_profit['operation'] = 'PROFIT'
        #     trades_profit.loc[trades_profit['profit']<0, 'type'] = 'PIERDERE'
        #     trades_profit['value'] = trades_profit['profit']
        #     trades_profit['quantity'] = 0
        #     trades_profit['details'] = 'profit'

        #     all = pd.concat([all, trades_profit, ], ignore_index=True)

        vals = all['operation'].unique()
        all_vals = Operation.objects.values_list('operation_code', flat=True)
        print('Missing keys:', set(vals) - set(all_vals))


        cols_not_null = ['value', 'quantity', 'unit_cost', 'profit',  'value_gross']
        all[cols_not_null] = all[cols_not_null].fillna(0)

        print(all)



        # Upload journal to database
        # Prepare update        
        model_instances = [
            Journal(
                # currency_code = Currency.objects.get(currency_code = row['currency']),
                ubo = Ubo.objects.get(ubo_code = row['ubo']),
                custodian = Custodian.objects.get(custodian_code = row['custodian']),
                account = Account.objects.get(account_code = row['account']),
                operation = Operation.objects.get(operation_code = row['operation']),
                partner = Partner.objects.get(partner_code = row['partner']),
                instrument = Instrument.objects.get(symbol=row['symbol'], custodian__custodian_code=row['custodian']),
                date = row['date'], 
                transactionid = row['transactionid'], 
                value = row['value'], 
                quantity = row['quantity'], 
                details = row['details'], 
                unit_cost = row['unit_cost'], 
                profit = row['profit'],
            )
            for i, row in all.iterrows()]
        
        # Upload model_instances to database
        unique = ['ubo', 'custodian', 'account', 'transactionid', 'operation']
        update_fields = unique + ['partner', 'instrument', 'date', 'value', 'quantity', 'details', 'unit_cost', 'profit',  'value_gross']
        Journal.objects.bulk_create(
            model_instances, 
            update_conflicts=True,
            unique_fields=unique,
            update_fields=update_fields,
        )


        
        print('MANUAL journals imported into database, max date imported is', max(all['date']))
        print('All done')
