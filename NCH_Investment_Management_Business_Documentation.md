# NCH Investment Management System
## Business Documentation

---

## Executive Summary

The NCH Investment Management System is a comprehensive financial portfolio management platform designed to automate the processing, reconciliation, and accounting of investment transactions from multiple brokers. The system integrates data from Interactive Brokers (IBKR) and Tradeville (TDV), processes complex financial instruments including bonds and equities, and generates accounting journal entries compliant with Romanian accounting standards.

### Key Business Value
- **Automated Data Integration**: Eliminates manual data entry from broker platforms
- **Real-time Portfolio Tracking**: Consolidated view across multiple custodians
- **Regulatory Compliance**: Automated accounting entries following Romanian GAAP
- **Risk Management**: Comprehensive position tracking and valuation
- **Operational Efficiency**: Reduces processing time from days to minutes

---

## System Overview

### Core Business Functions

1. **Broker Data Integration**
   - Automated download of transaction and position data
   - Real-time portfolio synchronization
   - Multi-currency support with automatic FX conversion

2. **Financial Instrument Processing**
   - Bond accrual calculations using QuantLib
   - Equity transaction processing
   - Currency position management
   - Derivative instrument support

3. **Accounting Integration**
   - Double-entry journal generation
   - Chart of accounts mapping
   - Multi-entity consolidation
   - Period-end closing procedures

4. **Reporting & Analytics**
   - Portfolio valuation reports
   - Performance analytics
   - Regulatory reporting
   - Risk metrics calculation

---

## Broker Integration Architecture

### Interactive Brokers (IBKR) Integration

#### Data Sources and File Types

**1. Flex Query Reports**
- **File Location**: `files/ibkr/raw/statements_YYYY-MM-DD.txt`
- **Format**: JSON (converted from XML)
- **Update Frequency**: Daily
- **Content**: Complete trading activity and positions

**Generated Files from IBKR:**

| File Type | Location | Content | Business Purpose |
|-----------|----------|---------|------------------|
| `OpenPositions_YYYY-MM-DD.csv` | `files/ibkr/daily/` | Current portfolio holdings | Position reconciliation |
| `CashReport_YYYY-MM-DD.csv` | `files/ibkr/daily/` | Cash balances by currency | Liquidity management |
| `SecuritiesInfo_YYYY-MM-DD.csv` | `files/ibkr/daily/` | Instrument master data | Reference data management |
| `Trades_YYYY-MM-DD.csv` | `files/ibkr/daily/` | Executed transactions | Transaction processing |
| `CorporateActions_YYYY-MM-DD.csv` | `files/ibkr/daily/` | Dividend, splits, etc. | Income recognition |
| `ChangeInDividendAccruals_YYYY-MM-DD.csv` | `files/ibkr/daily/` | Dividend accruals | Accrual accounting |

#### IBKR Journal Operations Created

**Trading Operations:**
- `BUY_BOND_BROKER`: Bond purchase transactions
- `SELL_BOND_BROKER`: Bond sale transactions  
- `SELL_BOND_BANCA`: Bond sales to banks
- `BUY_EQUITY_BROKER`: Equity purchase transactions
- `SELL_EQUITY_BROKER`: Equity sale transactions

**Income Operations:**
- `BOND_INTEREST_RECEIVED_BROKER`: Bond coupon payments
- `BOND_INTEREST_PAID_BROKER`: Bond interest paid (short positions)
- `INC_DIVIDEND_BROKER`: Dividend income received
- `BROKER_INTEREST_RECEIVED`: Interest on cash balances

**Fee and Tax Operations:**
- `COMIS_BROKER_VALUTA`: Trading commissions and fees
- `WHT_BROKER`: Withholding tax deductions
- `STAMP_DUTY_BROKER`: Stamp duty charges
- `VAT`: Value-added tax on services

**Cash Management Operations:**
- `VIRAMENT`: Deposits and withdrawals
- `FX_TRADE_BROKER`: Foreign exchange transactions

#### IBKR Data Processing Workflow

1. **Data Download** (`api_ibkr.py`)
   - Executes Flex Query via IBKR API
   - Downloads XML reports
   - Converts to JSON format
   - Saves raw data to `files/ibkr/raw/`

2. **Data Parsing** (`api_ibkr.py`)
   - Extracts individual tables from JSON
   - Converts to CSV format
   - Saves to `files/ibkr/daily/`

3. **Portfolio Import** (`upload_ibkr_port.py`)
   - Processes position data
   - Updates instrument master data
   - Imports current portfolio positions

4. **Transaction Processing** (`uptrz_ibkr.py`)
   - Processes trade and corporate action data
   - Maps IBKR transaction types to internal operations
   - Generates journal entries
   - Exports to `files/reports/journal_ibkr_YYYY-MM-DD.xlsx`

### Tradeville (TDV) Integration

#### Data Sources and File Types

**1. WebSocket API Connection**
- **Protocol**: WebSocket with JSON messaging
- **Authentication**: Username/password with sub-account selection
- **Update Frequency**: Twice daily (morning and afternoon)
- **Real-time Capability**: Live market data and order management

**Generated Files from TDV:**

| File Type | Location | Content | Business Purpose |
|-----------|----------|---------|------------------|
| `portof_YYYY-MM-DD.csv` | `files/tdv/` | Portfolio positions | Position tracking |
| `activitate_YYYY-MM-DD.csv` | `files/tdv/` | Transaction history | Trade processing |
| `ordine_YYYY-MM-DD.csv` | `files/tdv/` | Order book | Order management |

#### TDV Journal Operations Created

**Trading Operations:**
- `BUY_BOND_BROKER`: Bond purchases (operation: 'cump')
- `SELL_BOND_BROKER`: Bond sales (operation: 'vanz')
- `BUY_EQUITY_BROKER`: Equity purchases
- `SELL_EQUITY_BROKER`: Equity sales

**Income Operations:**
- `BOND_INTEREST_RECEIVED_BROKER`: Bond coupon receipts
- `BOND_INTEREST_PAID_BROKER`: Bond interest payments
- `INC_DIVIDEND_BROKER`: Dividend income

**Cash Management Operations:**
- `VIR_INT_IN_BROKER_VALUTA`: Cash deposits (operation: 'in')
- `VIR_INT_OUT_BROKER_VALUTA`: Cash withdrawals (operation: 'out')
- `TRANSFER_INTERN_BROKER`: Internal transfers between accounts

**Fee Operations:**
- `COMIS_BROKER_VALUTA`: Trading commissions
- `TAXE_BROKER`: Transaction taxes

#### TDV Data Processing Workflow

1. **Data Download** (`api_tdv.py`)
   - Establishes WebSocket connection
   - Authenticates with credentials
   - Queries portfolio positions (`portof`)
   - Queries transaction history (`activitate`)
   - Queries order book (`ordine`)

2. **Portfolio Import** (`upload_tdv_port.py`)
   - Processes position data
   - Creates missing instruments
   - Updates portfolio positions

3. **Transaction Processing** (`uptrz_tdv.py`)
   - Processes transaction data
   - Handles dirty price vs clean price for bonds
   - Separates interest components
   - Generates journal entries
   - Exports to `files/reports/journal_tdv_YYYY-MM-DD.xlsx`

---

## Bond Accruals Process

### Overview

The bond accruals process (`bond_accruals_ql.py`) is a sophisticated financial calculation engine that computes accrued interest for bond positions using QuantLib, a professional quantitative finance library. This process ensures accurate accounting for bond interest that has been earned but not yet received.

### Business Purpose

**Why Bond Accruals Matter:**
- **Accurate Valuation**: Reflects true economic value of bond positions
- **Period Matching**: Matches interest income to the correct accounting period
- **Regulatory Compliance**: Required for financial reporting standards
- **Performance Measurement**: Enables accurate return calculations

### QuantLib Integration

**QuantLib Capabilities Used:**
- **Day Count Conventions**: ACT/ACT, 30/360, ACT/360
- **Business Day Calendars**: TARGET, US, UK calendars
- **Coupon Scheduling**: Automatic payment date generation
- **Settlement Date Calculation**: T+0, T+1, T+2 settlement

### Accrual Calculation Process

#### 1. Data Collection

**Bond Information Required:**
- Symbol and ISIN identification
- Coupon rate (annual percentage)
- Issue date and maturity date
- Payment frequency (semi-annual, annual)
- Day count convention
- Business day calendar

**Transaction Data:**
- All bond transactions (purchases, sales)
- Coupon payments received
- Position changes over time

#### 2. Accrual Types Generated

**INTEREST_ACCRUAL_BOND Operations:**
- **Purpose**: Daily accrual of bond interest
- **Calculation**: (Position × Coupon Rate × Days) / Days in Year
- **Frequency**: End of each month
- **Accounting**: Debit 5088 (Interest Receivable), Credit 766 (Interest Income)

**BOND_COUPON_RECEIVED_BROKER Operations:**
- **Purpose**: Actual coupon payments received
- **Effect**: Reverses accumulated accruals
- **Accounting**: Debit Cash, Credit 5088 (Interest Receivable)

#### 3. Calculation Methodology

**Step-by-Step Process:**

1. **Position Tracking**
   ```
   For each bond:
   - Track daily position changes
   - Calculate cumulative holdings
   - Identify coupon payment dates
   ```

2. **Accrual Calculation**
   ```
   Daily Accrual = Position × (Coupon Rate / Payment Frequency) × 
                   (Days Since Last Coupon / Days in Coupon Period)
   ```

3. **Settlement Date Adjustment**
   ```
   - Trade Date + Settlement Days
   - Adjust for business day calendar
   - Handle coupon date settlements
   ```

4. **Incremental Accrual**
   ```
   Monthly Accrual = Current Total Accrual - Previous Total Accrual
   ```

#### 4. Special Handling

**Coupon Payment Processing:**
- **Reset Accruals**: Set total accruals to zero on coupon dates
- **Reconciliation**: Verify received amount matches accrued amount
- **Adjustments**: Create adjustment entries for differences

**Foreign Exchange Impact:**
- **Multi-Currency**: Handle bonds in EUR, USD, other currencies
- **FX Revaluation**: Monthly revaluation of foreign currency accruals
- **Rate Sources**: Use official BNR (Romanian Central Bank) rates

### Accrual Operations Differences

#### INTEREST_ACCRUAL_BOND vs BOND_INTEREST_RECEIVED_BROKER

| Aspect | INTEREST_ACCRUAL_BOND | BOND_INTEREST_RECEIVED_BROKER |
|--------|----------------------|------------------------------|
| **Timing** | Monthly (end of period) | Actual coupon payment date |
| **Purpose** | Accrue earned interest | Record cash receipt |
| **Amount** | Calculated incremental | Actual payment received |
| **Accounting** | Dr 5088, Cr 766 | Dr Cash, Cr 5088 |
| **Reversal** | Accumulates over time | Reverses accruals |

#### Daily vs Monthly Accruals

**Daily Accruals** (if implemented):
- More precise interest calculation
- Higher processing volume
- Real-time position valuation

**Monthly Accruals** (current implementation):
- Simplified processing
- Period-end accuracy
- Reduced transaction volume

### Output and Reporting

**Generated Files:**
- `files/accruals/bond_accruals_YYYY-MM-DD.xlsx`: Detailed calculations
- Journal entries for import into accounting system
- Reconciliation reports comparing accrued vs received

**Key Reports:**
- Accrual summary by bond and entity
- Foreign exchange impact analysis
- Coupon payment reconciliation
- Period-over-period accrual changes

---

## Operation Code Framework

### Operation Categories

The system uses a comprehensive operation code framework with 122+ predefined operations covering all aspects of investment management:

#### Trading Operations (20+ codes)
- **Bond Trading**: BUY_BOND_BROKER, SELL_BOND_BROKER, SELL_BOND_BANCA
- **Equity Trading**: BUY_EQUITY_BROKER, SELL_EQUITY_BROKER
- **FX Trading**: FX_TRADE_BROKER, FX_SPOT_TRADE

#### Income Operations (15+ codes)
- **Interest Income**: BOND_INTEREST_RECEIVED_BROKER, BROKER_INTEREST_RECEIVED
- **Dividend Income**: INC_DIVIDEND_BROKER, INC_DIVIDEND_BANCA
- **Accrual Operations**: INTEREST_ACCRUAL_BOND, ACCRUAL_INTEREST_VALUTA

#### Fee and Tax Operations (10+ codes)
- **Commissions**: COMIS_BROKER_VALUTA, COMIS_BANCA_VALUTA
- **Taxes**: WHT_BROKER, STAMP_DUTY_BROKER, VAT
- **Other Fees**: CUSTODY_FEE, MANAGEMENT_FEE

#### Cash Management Operations (15+ codes)
- **Transfers**: VIRAMENT, VIR_INT_IN_BROKER_VALUTA, VIR_INT_OUT_BROKER_VALUTA
- **Internal Moves**: TRANSFER_INTERN_BROKER, TRANSFER_INTERN_BANCA

#### Accrual and Adjustment Operations (20+ codes)
- **Bond Accruals**: INTEREST_ACCRUAL_BOND, BOND_ACCRUAL_REVERSAL
- **Deposit Accruals**: ACCRUAL_INTEREST_LEI, ACCRUAL_INTEREST_VALUTA
- **FX Adjustments**: DIF_CURS_NEREALIZAT, DIF_CURS_REALIZAT

### Accounting Integration

Each operation maps to specific accounting entries:
- **Debit Account**: Asset, expense, or contra-revenue account
- **Credit Account**: Liability, revenue, or contra-asset account
- **Multi-dimensional**: Support for currency, custodian, partner dimensions

---

## Data Flow and Processing Schedule

### Daily Operations Schedule

**Morning Processing (07:00-09:00):**
1. BNR exchange rate download
2. TDV data download and processing
3. Portfolio position updates

**Afternoon Processing (15:00-17:00):**
1. IBKR data download and processing
2. Transaction journal generation
3. Portfolio reconciliation

**Month-End Processing:**
1. Bond accrual calculations
2. FX revaluation
3. Period closing procedures

### Data Quality and Validation

**Automated Checks:**
- Position reconciliation between brokers and internal records
- Cash balance validation
- Foreign key integrity verification
- Duplicate transaction detection

**Exception Handling:**
- Missing operation codes flagged for manual review
- Unmatched instruments require master data updates
- Failed reconciliations generate alerts

---

## Business Benefits and ROI

### Operational Efficiency
- **Time Savings**: 95% reduction in manual data entry
- **Error Reduction**: Automated processing eliminates human errors
- **Scalability**: Handle unlimited transaction volumes

### Regulatory Compliance
- **Audit Trail**: Complete transaction history with timestamps
- **Standardization**: Consistent accounting treatment
- **Reporting**: Automated regulatory report generation

### Risk Management
- **Real-time Monitoring**: Immediate position updates
- **Concentration Limits**: Automated limit monitoring
- **Valuation Accuracy**: Mark-to-market pricing

### Cost Reduction
- **Staff Efficiency**: Reduce manual processing requirements
- **System Integration**: Eliminate duplicate data entry
- **Audit Costs**: Simplified audit procedures

---

## Appendix A: File Structure Details

### IBKR File Formats

**OpenPositions File Structure:**
```
symbol, currency, position, marketPrice, marketValue, averageCost, unrealizedPnL,
realizedPnL, accountId, model, securityID, securityIDType, cusip, isin,
listingExchange, underlyingCategory, underlyingSymbol, multiplier, strike,
expiry, putCall, principalAdjustFactor
```

**Trades File Structure:**
```
currency, symbol, dateTime, quantity, tradePrice, tradeMoney, proceeds, taxes,
ibCommission, ibCommissionCurrency, netCash, closePrice, openCloseIndicator,
notes, cost, fifoPnlRealized, fxPnl, mtmPnl, origTradePrice, origTradeDate,
origTradeID, origOrderID, clearingFirmID, transactionID, buySell,
ibOrderID, ibExecID, brokerageOrderID, orderReference, volatilityOrderLink,
exchOrderId, extExecID, orderTime, openDateTime, holdingPeriod,
whenRealized, whenReopened, levelOfDetail, changeInPrice, changeInQuantity,
orderType, traderID, isAPIOrder
```

### TDV File Formats

**Portfolio (portof) File Structure:**
```
valuta, simbol, isin, nume, firma, sold, costm, ppiata, valoare,
profit, procent, tip, data_actualizare
```

**Activity (activitate) File Structure:**
```
id, op, simbol, data, firma, suma, cant, descr, valuta, costm,
pret, dirty, comis, txcnlei, profit, status, data_executie
```

---

## Appendix B: Complete Operation Mapping

### IBKR Operation Mapping
```
IBKR Transaction Type → Internal Operation Code
"DEPOSITS/WITHDRAWALS" → "VIRAMENT"
"BOND INTEREST PAID" → "BOND_INTEREST_PAID_BROKER"
"BOND INTEREST RECEIVED" → "BOND_INTEREST_RECEIVED_BROKER"
"WITHHOLDING TAX" → "WHT_BROKER"
"BROKER INTEREST PAID" → "BROKER_INTEREST_PAID_BROKER"
"BROKER INTEREST RECEIVED" → "BROKER_INTEREST_RECEIVED"
"OTHER FEES" → "COMIS_BROKER_VALUTA"
"TM" → "SELL_BOND"
"BM" → "SELL_BOND_BROKER"
"VAT" → "COMIS_BROKER_VALUTA"
"BUY" → "BUY_BOND_BROKER"
"SELL" → "SELL_BOND_BANCA"
"DIVIDENDS" → "INC_DIVIDEND_BROKER"
"COMMISSION ADJUSTMENTS" → "COMIS_BROKER_VALUTA"
"STAMP_DUTY_BROKER" → "STAMP_DUTY_BROKER"
```

### TDV Operation Mapping
```
TDV Operation → Internal Operation Code
"cump" → "BUY_BOND_BROKER" (with interest split)
"vanz" → "SELL_BOND_BROKER" (with interest split)
"in" → "VIR_INT_IN_BROKER_VALUTA" (deposits)
"out" → "VIR_INT_OUT_BROKER_VALUTA" (withdrawals)
"div" → "INC_DIVIDEND_BROKER"
"dobanda" → "BOND_INTEREST_RECEIVED_BROKER"
```

### Bond Interest Separation Logic (TDV)
When TDV reports a bond transaction with "dirty price" (including accrued interest):
1. **Main Transaction**: Uses clean price (dirty - accrued)
2. **Interest Component**: Separate journal entry for accrued interest
   - Buy transactions: `BOND_INTEREST_PAID_BROKER`
   - Sell transactions: `BOND_INTEREST_RECEIVED_BROKER`

---

## Appendix C: Account Structure

### Account Naming Convention
- **IBKR Accounts**: `IBKR_[CURRENCY]` (e.g., IBKR_EUR, IBKR_USD)
- **TDV Accounts**: `TDV_[CURRENCY]` or `TDV_RE` for Romanian equity accounts
- **Special Accounts**: Based on transaction details and currency

### Multi-Entity Support
The system supports multiple Ultimate Beneficial Owners (UBOs):
- **DD**: Primary entity
- **AS**: Secondary entity
- **CP**: Third entity

Each entity maintains separate:
- Portfolio positions
- Cash balances
- P&L calculations
- Regulatory reporting

---

## Appendix D: Bond Accrual Technical Details

### QuantLib Configuration
```python
# Calendar mappings
calendar_map = {
    "TARGET": ql.TARGET(),
    "US": ql.UnitedStates(),
    "UK": ql.UnitedKingdom()
}

# Day count conventions
paymentConvention = {
    "ACT/ACT": ql.ActualActual(ql.ActualActual.ISMA),
    "30/360": ql.Thirty360(ql.Thirty360.European),
    "ACT/360": ql.Actual360()
}
```

### Accrual Calculation Formula
```
Accrued Interest = Face Value × Coupon Rate × (Days Accrued / Days in Period)

Where:
- Face Value = Position × Bond Face Value
- Coupon Rate = Annual rate / Payment Frequency
- Days Accrued = Settlement Date - Last Coupon Date
- Days in Period = Next Coupon Date - Last Coupon Date
```

### Settlement Date Rules
- **Bonds**: T+2 settlement (trade date + 2 business days)
- **Coupon Dates**: T+0 settlement (same day)
- **Business Day Adjustment**: Following business day convention

---

## Appendix E: Error Handling and Validation

### Data Validation Rules
1. **Missing Operations**: All transaction types must map to valid operation codes
2. **Missing Instruments**: All symbols must exist in instrument master data
3. **Position Reconciliation**: Portfolio positions must match transaction history
4. **Cash Reconciliation**: Cash flows must balance by currency and entity
5. **Foreign Key Integrity**: All references must exist in master data tables

### Exception Reports
- **Missing Operations Report**: Lists unmapped transaction types requiring setup
- **Missing Instruments Report**: Identifies new securities requiring master data creation
- **Reconciliation Differences**: Highlights position or cash discrepancies
- **Processing Errors**: Technical errors during data processing

### Data Quality Metrics
- **Processing Success Rate**: Percentage of transactions processed without errors
- **Reconciliation Accuracy**: Variance between broker and internal positions
- **Timeliness**: Processing completion within SLA timeframes
- **Completeness**: All expected files received and processed

---

*This comprehensive business documentation covers all aspects of the NCH Investment Management System's broker integration, bond accrual processing, and operational procedures. The system provides a robust foundation for multi-broker portfolio management with automated accounting integration.*
