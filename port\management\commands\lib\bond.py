import QuantLib as ql
import pandas as pd
from pandas.tseries.offsets import BDay, MonthEnd
import numpy as np
from datetime import datetime, timedelta
import holidays
from zoneinfo import ZoneInfo
from dateutil.easter import easter

from port.models import Journal, Bnr, Instrument


class EnhancedBondAccrualCalculator:


    dayCounters = {
        # 'SimpleDayCounter': ql.SimpleDayCounter(),
        '30/360': ql.Thirty360(ql.Thirty360.ISDA),
        'ISMA-30/360': ql.Thirty360(ql.Thirty360.European),
        'Actual360': ql.Actual360(),
        'Actual365Fixed': ql.Actual365Fixed(),
        'Actual365Fixed(Canadian)': ql.Actual365Fixed(ql.Actual365Fixed.Canadian),
        'Actual365FixedNoLeap': ql.Actual365Fixed(ql.Actual365Fixed.NoLeap),
        'ACT/ACT': ql.ActualActual(ql.ActualActual.ISDA),
        # 'Business252': ql.Business252()
    }

    def __init__(self):
        self.calendar = ql.TARGET()  # European calendar
        self.day_counter = ql.Actual360() # Can be changed to other conventions
        
    def calculate_accrued_interest(self, 
                                 face_value: float,
                                 coupon_rate: float,
                                 start_date: datetime,
                                 end_date: datetime,
                                 day_count_convention: str = 'Actual360'):
        """
        Calculate accrued interest using QuantLib
        
        Args:
            face_value: Nominal amount
            coupon_rate: Annual interest rate (%)
            start_date: Start of accrual period
            end_date: End of accrual period
            day_count_convention: Day count method
        """
        # Convert dates to QuantLib format
        ql_start = ql.Date(start_date.day, start_date.month, start_date.year)
        ql_end = ql.Date(end_date.day, end_date.month, end_date.year)
        
        # Select day count convention
        if day_count_convention == 'Actual360':
            day_counter = ql.Actual360()
        elif day_count_convention == 'Actual365Fixed':
            day_counter = ql.Actual365Fixed()
        elif day_count_convention == 'Thirty360':
            day_counter = ql.Thirty360()
        
        # Calculate year fraction
        year_fraction = day_counter.yearFraction(ql_start, ql_end)
        
        # Calculate accrued interest
        accrued_interest = face_value * (coupon_rate/100) * year_fraction
        
        return accrued_interest

    def get_payment_schedule(self, 
                           issue_date: datetime,
                           maturity_date: datetime,
                           payment_frequency: int = 2):
        """Generate payment schedule using QuantLib"""
        ql_issue = ql.Date(issue_date.day, issue_date.month, issue_date.year)
        ql_maturity = ql.Date(maturity_date.day, maturity_date.month, maturity_date.year)
        
        # Create schedule
        schedule = ql.Schedule(
            ql_issue,
            ql_maturity,
            ql.Period(12//payment_frequency, ql.Months),
            self.calendar,
            ql.Following,
            ql.Following,
            ql.DateGeneration.Forward,
            False
        )
        
        # Convert to pandas datetime
        payment_dates = [datetime(d.year(), d.month(), d.dayOfMonth()) 
                        for d in schedule]
        
        return pd.DataFrame({
            'payment_date': payment_dates
        })

class BondAccrualCalculator:
    def __init__(self):
        # Initialize base holiday calendars
        self.us_holidays = holidays.US()
        self.de_holidays = holidays.DE()
        
        # Add Good Friday to US holidays
        self.good_fridays = {}
        # Pre-calculate Good Fridays for next few years
        for year in range(2024, 2027):
            easter_date = easter(year)
            good_friday = easter_date - timedelta(days=2)
            self.good_fridays[good_friday] = "Good Friday"
        
    def is_business_day(self, date, currency=None):
        """Check if date is a business day based on currency
        
        Args:
            date: The date to check
            currency: The currency of the bond (USD, EUR, etc)
        """
        date = pd.Timestamp(date)
        
        # Basic weekend check
        if date.weekday() >= 5:  # Saturday or Sunday
            return False
            
        # Good Friday check for all currencies
        if date.date() in self.good_fridays:
            return False
            
        # USD bonds only check US holidays
        if currency == 'USD':
            return date not in self.us_holidays
            
        # Other bonds (including EUR) check both calendars
        return (date not in self.us_holidays and 
                date not in self.de_holidays)

    def next_business_day(self, date, currency=None):
        """Get next business day based on currency"""
        date = pd.Timestamp(date)
        while not self.is_business_day(date, currency):
            date += timedelta(days=1)
        return date


    def validate_inputs(self, bond_issue, bond_first_coupon, maturity, bond_coupon_count): 
        """Validate input parameters"""
        # Convert string dates to timestamps if needed
        bond_issue = pd.Timestamp(bond_issue).date()
        bond_first_coupon = pd.Timestamp(bond_first_coupon).date()
        maturity = pd.Timestamp(maturity).date()
        
        if bond_first_coupon < bond_issue:
            raise ValueError("First coupon date cannot be before issue date")
            
        if maturity < bond_first_coupon:
            raise ValueError("Maturity date cannot be before first coupon date")
            
        if bond_coupon_count not in [0, 1, 2]:
            raise ValueError("Coupon count must be 0, 1 (annual) or 2 (semi-annual)")
            
        return bond_issue, bond_first_coupon, maturity, bond_coupon_count

    # def calculate_coupon_dates(self, bond_issue, bond_first_coupon, maturity, bond_coupon_count):
    #     """Calculate all coupon payment dates for a bond"""
    #     # Validate inputs
    #     bond_issue, bond_first_coupon, maturity, bond_coupon_count = self.validate_inputs(
    #         bond_issue, bond_first_coupon, maturity, bond_coupon_count
    #     )
        
    #     # Calculate period between coupons (in months)
    #     months_between_coupons = 12 // bond_coupon_count
        
    #     # Initialize list with first coupon date
    #     coupon_dates = [bond_first_coupon]
        
    #     # Calculate subsequent coupon dates
    #     current_date = bond_first_coupon
    #     while current_date < maturity:
    #         # Add months_between_coupons to get next theoretical coupon date
    #         current_date += pd.DateOffset(months=months_between_coupons)
            
    #         # Don't include dates after maturity
    #         if current_date > maturity:
    #             break
                
    #         # # Adjust for business day
    #         # current_date = self.next_business_day(current_date)
            
    #         coupon_dates.append(current_date)
        
    #     # Add maturity date if it's different from last coupon date
    #     if maturity != coupon_dates[-1]:
    #         maturity = self.next_business_day(maturity)
    #         coupon_dates.append(maturity)

    #     print(bond_issue, bond_first_coupon, maturity, bond_coupon_count)
    #     print(coupon_dates)
        
    #     return coupon_dates
    
    def get_coupon_schedule(self, bond_issue, bond_first_coupon, maturity, bond_coupon_count):
        """Get a formatted schedule of all coupon dates"""

        # Validate inputs
        bond_issue, bond_first_coupon, maturity, bond_coupon_count = self.validate_inputs(
            bond_issue, bond_first_coupon, maturity, bond_coupon_count
        )   

        # Initialize list with first coupon date
        coupon_dates = [bond_first_coupon]
        
        # Calculate subsequent coupon dates - yearly
        current_date = bond_first_coupon
        while current_date < maturity:
            current_date = (current_date + pd.DateOffset(years=1)).date()
            if current_date > maturity: # Don't include dates after maturity
                break
            coupon_dates.append(current_date)
        
        if bond_coupon_count == 2:
            # Calculate subsequent coupon dates at each bond anniversary
            current_date = bond_issue 
            while current_date < maturity:
                current_date = (current_date + pd.DateOffset(years=1)).date()
                if current_date > maturity: # Don't include dates after maturity
                    break
                coupon_dates.append(current_date)
       
        coupon_dates = sorted(coupon_dates)

        schedule = pd.DataFrame({
            # 'coupon_date': coupon_dates,
            'last_coupon_date': [bond_issue] + coupon_dates,
            'coupon_number': range(0, len(coupon_dates) + 1)
        })

        schedule['next_coupon_date'] = schedule['last_coupon_date'].shift(-1)
        
        return schedule
    
    def get_last_trading_day(self, date):
        """Get the last trading day of the month for both US and Germany markets"""
        date = pd.Timestamp(date)
        last_day = pd.Timestamp(date.year, date.month, 1) + MonthEnd(1)
        
        while not self.is_business_day(last_day):
            last_day -= timedelta(days=1)
            
        return last_day
    
    def is_last_trading_day(self, date):
        """Check if date is the last trading day of its month"""
        return self.get_last_trading_day(date) == pd.Timestamp(date)
    
    def get_settlement_date(self, trade_date, currency):
        """Calculate settlement date based on T+2/T+1 rule and currency"""
        T1CUTOFF = "2024-05-28"
        trade_date = pd.Timestamp(trade_date)

        # Determine settlement delay
        if currency == 'USD':
            days_to_settle = 1
        else:
            is_post_change = trade_date >= pd.Timestamp(T1CUTOFF)
            days_to_settle = 1 if is_post_change else 2
        
        settlement_date = trade_date
        days_counted = 0
        
        while days_counted < days_to_settle:
            settlement_date += timedelta(days=1)
            if self.is_business_day(settlement_date, currency):
                days_counted += 1
                
        # Ensure final date is a business day
        while not self.is_business_day(settlement_date, currency):
            settlement_date += timedelta(days=1)
            
        return settlement_date
    
    def calculate_accrual_days(self, start_date, end_date, due_date):
        """Calculate accrual days considering leap years"""
        start_date = pd.Timestamp(start_date)
        end_date = pd.Timestamp(end_date)
        due_date = pd.Timestamp(due_date)
        
        coupon_year = due_date.year
        is_leap_year = (coupon_year % 4 == 0 and coupon_year % 100 != 0) or coupon_year % 400 == 0
        days_in_year = 366 if is_leap_year else 365
        
        return (end_date - start_date).days, days_in_year
    
    def calculate_accrued_interest(self, face_value, interest_rate, accrual_days, days_in_year):
        """Calculate accrued interest amount"""
        return (face_value * interest_rate * accrual_days) / (days_in_year * 100)
    
    def calculate_transaction_accrual(self, date, quantity, bond_info, prev_coupon_date):
        """Calculate accrual for a specific transaction amount"""
        settlement_date = self.get_settlement_date(date)
        next_coupon = self.get_next_coupon_date(date, bond_info)
        
        accrual_days, days_in_year = self.calculate_accrual_days(
            prev_coupon_date, settlement_date, next_coupon
        )
        
        return self.calculate_accrued_interest(
            abs(quantity),
            bond_info['interest'],
            accrual_days,
            days_in_year
        ), accrual_days, days_in_year, settlement_date
    
    def calculate_balance_accrual(self, current_position, date, prev_date, bond_info):
        """Calculate accrual for the running balance"""
        if current_position == 0:
            return 0, 0, 0, None
            
        settlement_date = self.get_settlement_date(date)
        next_coupon = self.get_next_coupon_date(settlement_date, bond_info)
        
        accrual_days, days_in_year = self.calculate_accrual_days(
            prev_date, settlement_date, next_coupon
        )
        
        return self.calculate_accrued_interest(
            current_position,
            bond_info['interest'],
            accrual_days,
            days_in_year
        ), accrual_days, days_in_year, settlement_date
    
    def adjust_coupon_date(self, date):
        """Adjust coupon date to next business day if needed"""
        date = pd.Timestamp(date)
        while not self.is_business_day(date):
            date += timedelta(days=1)
        return date


    def query_data(self):
        journal_qs = Journal.objects.filter(
            operation__operation_code__contains='BOND'
            ).select_related(
                'operation',
                'instrument',
                'instrument__currency',
            ).values(
                'id',
                'ubo__ubo_code',
                'custodian__custodian_code',
                'partner__partner_code',
                'operation__operation_code',
                'instrument__symbol',
                'instrument__currency__currency_code',
                'account__account_code',
                'date',
                'value',
                'quantity',
                'bnr',
                'value_ron',
                'details',
            )
        
        df_bonds = pd.DataFrame(list(journal_qs)).rename(columns={
                'instrument__currency__currency_code': 'currency',
                'operation__operation_code': 'operation',
                'instrument__symbol': 'symbol',
                'instrument__convention': 'convention',
                'partner__partner_code': 'partner',
                'custodian__custodian_code': 'custodian',
                'account__account_code': 'account',
                'ubo__ubo_code': 'ubo',
            })
        if len(df_bonds) > 0:
            print(f"Found {len(df_bonds)} bond operations")
            df_bonds['instrument'] = df_bonds['custodian'].astype(str) + '_' + df_bonds['symbol'].astype(str)
            # print(df_bonds)
        else:
            print("No bond operations found")

        # 2. Query BNR rates
        bnr_qs = Bnr.objects.filter(
            currency_code__currency_code__in=['EUR', 'USD', 'MXN']
            ).select_related(
                'currency_code'
            ).values(
                'currency_code__currency_code',
                'date',
                # 'value',
                'value_exact'
            )
        
        df_bnr = pd.DataFrame(list(bnr_qs)).rename(columns={
            'currency_code__currency_code': 'currency',
            # 'value': 'bnr_ieri',
            'value_exact': 'bnr',
            })

        # Remove holidays
        df_bnr = df_bnr[~df_bnr['bnr'].isna()]

        # Ensure date is datetime type
        df_bnr["date"] = pd.to_datetime(df_bnr["date"])

        # Sort by date and currency
        df_bnr.sort_values(['date', 'currency'], inplace=True)
        
        # Create month column for grouping
        df_bnr['month'] = df_bnr['date'].dt.to_period('M')

        # Get the latest date for each month and currency combination
        df_bnr_eom = df_bnr.drop_duplicates(subset=['currency', 'month'], keep='last').copy()
        
        # Drop the temporary month column if you don't need it
        df_bnr = df_bnr.drop(columns=['month', ])
        df_bnr_eom = df_bnr_eom.drop(columns=['month', ]).rename(columns={'bnr': 'bnr_eom'})


        # 3. Query Instruments for bonds
        instruments_qs = Instrument.objects.filter(
            type='BOND'
            ).select_related(
                'currency',
                'custodian'
            ).values(
                'symbol',
                'bond_issue',
                'bond_first_coupon',
                'maturity',
                'interest',
                'convention', 
                'currency__currency_code',
                'bond_coupon_count',
                'face_value',
            )
        
        df_bond_info = pd.DataFrame(list(instruments_qs)).rename(columns={
                'currency__currency_code': 'currency',
            })
        if len(df_bond_info) > 0:
            print(f"Found {len(df_bond_info)} bond instruments")
        else:
            print("No bond instruments found")

        # Remove former accruals
        removed_ops = [
            'INTEREST_ACCRUAL_BOND', 'BOND_ACCRUAL_REVERSAL',
            'FX_DIFF_ACCRUAL_MINUS', 'FX_DIFF_ACCRUAL_PLUS',
            ]
        df_bonds = df_bonds[~df_bonds['operation'].isin(removed_ops)]

        # Remove bnr 
        df_bonds = df_bonds.drop(columns=['bnr', 'value_ron'])

        return df_bonds, df_bond_info, df_bnr, df_bnr_eom