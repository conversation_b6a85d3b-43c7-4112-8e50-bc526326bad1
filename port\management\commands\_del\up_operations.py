
""" Export Morgan Stanley instruments and portfolio"""
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings 
from port.models import Operation

class Command(BaseCommand):

    def handle(self, *args, **options):
        import pandas as pd

        file = settings.FILE_ROOT + "tables/operations.csv"
        df = pd.read_csv(file)

        # Upload operatons to database
        for index, row in df.iterrows():
            codex = Operation.objects.filter(code=row['code'])
            if len(codex)==0:
                obj = Operation(
                    code=row['code'],
                    name=row['name'],
                    credit=row['credit'],
                    credit_name=row['credit_name'],
                    debit=row['debit'],
                    debit_name=row['debit_name'],
                )
                # obj.save()
            else:
                # print('existing', existing)
                pass

        print(len(df), 'records')
        print('All done')


