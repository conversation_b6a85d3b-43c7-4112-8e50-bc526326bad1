from django.core.management.base import BaseCommand
from port.models import Accounting, Operation

OPERATIONS = [
    (132, "ACCRUAL_INTEREST_LEI", "Accrual dobanzi in lei", "5088", "766"),
    (
        118,
        "ACCRUAL_INTEREST_VALUTA",
        "Accrual interest depozit in valuta",
        "5088",
        "766",
    ),
    (139, "ACCRUAL_REVERSAL_LEI", "ACCRUAL_REVERSAL_LEI", "766", "5088"),
    (138, "ACCRUAL_REVERSAL_VALUTA", "ACCRUAL_REVERSAL_VALUTA", "766", "5088"),
    (
        83,
        "ACORDARE_IMPRUMUT_CATRE_AFILIAT_LEI",
        "ACORDARE_IMPRUMUT_CATRE_AFILIAT_LEI",
        "4511",
        "5121",
    ),
    (
        64,
        "ACORDARE_IMPRUMUT_CATRE_AFILIAT_VALUTA",
        "ACORDARE_IMPRUMUT_CATRE_AFILIAT_VALUTA",
        "4511",
        "5124",
    ),
    (135, "BOND_ACCRUAL_REVERSAL", "BOND_ACCRUAL_REVERSAL", "766", "5088"),
    (
        163,
        "BOND_COUPON_ADJUSTMENT",
        "Ajustare diferențe între acrruals si cupon primit",
        "5088",
        "766",
    ),
    (
        141,
        "BOND_COUPON_RECEIVED_BANCA",
        "BOND_COUPON_RECEIVED_BANCA",
        "5124",
        "5088",
    ),
    (
        134,
        "BOND_COUPON_RECEIVED_BROKER",
        "BOND_COUPON_RECEIVED_BROKER",
        "461",
        "5088",
    ),
    (
        153,
        "BOND_INTEREST_PAID_BANCA",
        "BOND_INTEREST_PAID_BANCA",
        "5088",
        "5124",
    ),
    (
        84,
        "BOND_INTEREST_PAID_BROKER",
        "BOND_INTEREST_PAID_BROKER",
        "5088",
        "461",
    ),
    (
        142,
        "BOND_INTEREST_RECEIVED_BANCA",
        "BOND_INTEREST_RECEIVED_BANCA",
        "5124",
        "5088",
    ),
    (
        85,
        "BOND_INTEREST_RECEIVED_BROKER",
        "BOND_INTEREST_RECEIVED_BROKER",
        "461",
        "5088",
    ),
    (
        126,
        "BOND_INTEREST_RECEIVED_DIF",
        "BOND_INTEREST_RECEIVED (DIF_RECUNOASTERE_VENIT)",
        "5088",
        "766",
    ),
    (
        154,
        "BROKER_INTEREST_PAID_BANCA",
        "BROKER_INTEREST_PAID_BANCA",
        "666",
        "5124",
    ),
    (
        86,
        "BROKER_INTEREST_PAID_BROKER",
        "BROKER_INTEREST_PAID_BROKER",
        "666",
        "461",
    ),
    (87, "BROKER_INTEREST_RECEIVED", "BROKER_INTEREST_RECEIVED", "461", "766"),
    (
        143,
        "BROKER_INTEREST_RECEIVED_BANCA",
        "BROKER_INTEREST_RECEIVED_BANCA",
        "5124",
        "766",
    ),
    (155, "BUY_BOND_BANCA", "BUY_BOND_BANCA", "506", "5124"),
    (4, "BUY_BOND_BROKER", "Cumparare obligațiuni prin broker", "506", "461"),
    (
        136,
        "BUY_MONEY_MARKET",
        "Plasamente in fonduri money market",
        "5081",
        "5124",
    ),
    (156, "BUY_STOCK_BANCA", "BUY_STOCK_BANCA", "5081", "5124"),
    (102, "BUY_STOCK_BROKER", "Cumparare acțiuni", "5081", "461"),
    (169, "BUY_STOCK_FUND", "BUY_STOCK_FUND", "265", "5124"),
    (121, "CAPITALIZARE_ACCRUALS", "Capitalizare accrual", "5124", "5088"),
    (78, "CL_RON", "Factura client RON", "5121", "4111"),
    (12, "COMIS_BANCA_LEI", "COMIS_BANCA_LEI", "627", "5121"),
    (13, "COMIS_BANCA_VALUTA", "COMIS_BANCA_VALUTA", "627", "5124"),
    (1, "COMIS_BROKER_LEI", "COMIS_BROKER_LEI", "622", "461"),
    (157, "COMIS_BROKER_LEI_BANCA", "COMIS_BROKER_LEI_BANCA", "622", "5121"),
    (76, "COMIS_BROKER_VALUTA", "COMIS_BROKER_VALUTA", "622", "461"),
    (
        158,
        "COMIS_BROKER_VALUTA_BANCA",
        "COMIS_BROKER_VALUTA_BANCA",
        "622",
        "5124",
    ),
    (18, "CONSTITUIRE_DEP_LEI", "CONSTITUIRE_DEP_LEI", "5081", "5121"),
    (23, "CONSTITUIRE_DEP_VALUTA", "CONSTITUIRE_DEP_VALUTA", "5081", "5124"),
    (74, "__DE_CLARIFICAT", "DE_CLARIFICAT MANUAL", "5121", "5121"),
    (
        123,
        "FX_DIF_ACCRUAL_MINUS",
        "Inregistrare diferente de curs nefavorabile la accrual depozit in valuta",
        "6651",
        "5088",
    ),
    (
        122,
        "FX_DIF_ACCRUAL_PLUS",
        "Inregistrare diferente de curs favorabile la accruals depozit in valuta",
        "5088",
        "7651",
    ),
    (
        167,
        "FX_DIF_BOND_MINUS",
        "Diferenţe favorabile de curs valutar legate de obligatiunule în valută",
        "6642DIF",
        "506",
    ),
    (
        166,
        "FX_DIF_BOND_PLUS",
        "Inregistrare diferente de curs favorabile la obligațiunile in valuta",
        "506",
        "7642DIF",
    ),
    (
        120,
        "FX_DIF_DEP_MINUS",
        "Diferenţe favorabile de curs valutar legate de elementele monetare exprimate în valută",
        "6651",
        "5081",
    ),
    (
        119,
        "FX_DIF_DEP_PLUS",
        "Inregistrare diferente de curs favorabile la depozitul in valuta",
        "5081",
        "7651",
    ),
    (
        124,
        "FX_DIF_DIN_FX_MINUS",
        "Diferențe schimb valutar nefavorabile rezultate din operațiuni de schimb valutar",
        "6651",
        "581",
    ),
    (
        127,
        "FX_DIF_DIN_FX_PLUS",
        "Diferențe schimb valutar favorabile rezultate din operațiuni de schimb valutar",
        "581",
        "7651",
    ),
    (171, "FX_DIF_MM_MINUS", "FX_DIF_MM_MINUS", "6642DIF", "5081"),
    (170, "FX_DIF_MM_PLUS", "FX_DIF_MM_PLUS", "5081", "7642DIF"),
    (
        164,
        "FX_DIF_VIR_INT_MINUS",
        "Diferente nefavorabile de curs din viramente in valuta non-intraday",
        "6651",
        "581",
    ),
    (
        165,
        "FX_DIF_VIR_INT_PLUS",
        "Diferente favorabile de curs din viramente in valuta non-intraday",
        "581",
        "7651",
    ),
    (105, "FX_IN_BANCA_RON", "FX_IN_BANCA_RON", "5121", "581"),
    (107, "FX_IN_BANCA_VALUTA", "FX_IN_BANCA_VALUTA", "5124", "581"),
    (103, "FX_IN_BROKER", "FX_IN_BROKER", "461", "581"),
    (144, "FX_IN_BROKER_BANCA", "FX_IN_BROKER_BANCA", "5124", "581"),
    (106, "FX_OUT_BANCA_RON", "FX_OUT_BANCA_RON", "581", "5121"),
    (108, "FX_OUT_BANCA_VALUTA", "FX_OUT_BANCA_VALUTA", "581", "5124"),
    (104, "FX_OUT_BROKER", "FX_OUT_BROKER", "581", "461"),
    (159, "FX_OUT_BROKER_BANCA", "FX_OUT_BROKER_BANCA", "581", "5124"),
    (79, "FZ_RON", "Furnizor RON", "401", "5121"),
    (80, "FZ_VALUTA", "Furnizori valuta", "401", "5124"),
    (
        68,
        "INCASARE_DOBANDA_IMPRUMUT_DE_LA_AFILIAT_LEI",
        "INCASARE_DOBANDA_IMPRUMUT_DE_LA_AFILIAT_LEI",
        "5121",
        "4518",
    ),
    (81, "INCASARE_FZ_462_RON", "INCASARE_FZ_462_RON", "5121", "462"),
    (
        40,
        "INCASARE_IMPRUMUT_DE_LA_ASOCIAT_VALUTA",
        "INCASARE_IMPRUMUT_DE_LA_ASOCIAT_VALUTA",
        "5124",
        "4551",
    ),
    (
        38,
        "INCASARE_VANZARE_PARTICIPATII _VALUTA",
        "INCASARE_VANZARE_PARTICIPATII _VALUTA",
        "5124",
        "461",
    ),
    (117, "INC_DIVIDEND_BROKER", "INC_DIVIDEND_BROKER", "461", "762"),
    (
        145,
        "INC_DIVIDEND_BROKER_BANCA",
        "INC_DIVIDEND_BROKER_BANCA",
        "5124",
        "762",
    ),
    (19, "INC_DIV_LEI", "INC_DIV_LEI", "5121", "4511"),
    (22, "INC_DIV_VALUTA", "INC_DIV_VALUTA", "5124", "4511"),
    (116, "INC_DOBANDA_LEI", "INCASARE DOBANDA BANCARA LEI", "5121", "766"),
    (34, "INC_DOBANDA_VALUTA", "INC_DOBANDA_VALUTA", "5124", "766"),
    (100, "INC_FZ", "Refund de la furnizor", "5121", "401"),
    (
        109,
        "INC_VANZARE_PART_VALUTA_BROKER",
        "Incasare la broker din vânzare participați",
        "461",
        "461",
    ),
    (
        146,
        "INC_VANZARE_PART_VALUTA_BROKER_BANCA",
        "INC_VANZARE_PART_VALUTA_BROKER_BANCA",
        "5124",
        "461",
    ),
    (133, "INTEREST_ACCRUAL_BOND", "INTEREST_ACCRUAL_BOND", "5088", "766"),
    (101, "MAJ_CAP_SOC_LA_AFILIATI", "MAJ_CAP_SOC_LA_AFILIATI", "456", "5121"),
    (17, "MAJ_CAP_SOC_LEI", "MAJ_CAP_SOC_LEI", "5121", "456"),
    (42, "MAJ_CAP_SOC_VALUTA", "MAJ_CAP_SOC_VALUTA", "5124", "456"),
    (16, "MATURITATE_DEP_LEI", "MATURITATE_DEP_LEI", "5121", "5081"),
    (33, "MATURITATE_DEP_VALUTA", "MATURITATE_DEP_VALUTA", "5124", "5081"),
    (115, "NEDEFINIT", "OPERATIE NEDEFINITA", "5121", "5121"),
    (
        128,
        "PIERDERE_INVESTITII_BOND",
        "PIERDERE_INVESTITII_BOND",
        "6642",
        "506",
    ),
    (
        82,
        "PIERDERE_INVESTITII_STOCK",
        "PIERDERE_INVESTITII_STOCK",
        "6642",
        "506",
    ),
    (
        28,
        "PLATA CESIUNE PARTI SOCIALE",
        "PLATA CESIUNE PARTI SOCIALE",
        "261",
        "5121",
    ),
    (
        90,
        "PLATA_DOBANDA_IMPRUMUT_CATRE_ASOCIAT_LEI",
        "PLATA_DOBANDA_IMPRUMUT_CATRE_ASOCIAT_LEI",
        "4558",
        "5121",
    ),
    (
        44,
        "PLATA_DOBANDA_IMPRUMUT_CATRE_ASOCIAT_VALUTA",
        "PLATA_DOBANDA_IMPRUMUT_CATRE_ASOCIAT_VALUTA",
        "4558",
        "5124",
    ),
    (8, "PLATA_DOBANDA_LEI", "PLATA_DOBANDA_LEI", "666", "5121"),
    (75, "PLATA_DOB_VALUTA", "PLATA_DOB_VALUTA", "666", "5124"),
    (110, "PLATA_FZ_462_RON", "PLATA_FZ_462_RON", "462", "5121"),
    (
        66,
        "PLATA_IMPOZITE_CONTRIBUTII",
        "PLATA_IMPOZITE_CONTRIBUTII",
        "5125",
        "5121",
    ),
    (129, "PROFIT_INVESTITII_BOND", "PROFIT_INVESTITII_STOCK", "461", "7642"),
    (
        147,
        "PROFIT_INVESTITII_BOND_BANCA",
        "PROFIT_INVESTITII_BOND_BANCA",
        "5124",
        "7642",
    ),
    (6, "PROFIT_INVESTITII_STOCK", "PROFIT_INVESTITII_STOCK", "461", "7642"),
    (
        148,
        "PROFIT_INVESTITII_STOCK_BANCA",
        "PROFIT_INVESTITII_STOCK_BANCA",
        "5124",
        "7642",
    ),
    (
        67,
        "RAMBURSARE_IMPRUMUT_DE_CATRE_AFILIAT_LEI",
        "RAMBURSARE_IMPRUMUT_DE_CATRE_AFILIAT_LEI",
        "5121",
        "4511",
    ),
    (
        89,
        "RAMBURSARE_IMPRUMUT_DE_CATRE_AFILIAT_VALUTA",
        "RAMBURSARE_IMPRUMUT_DE_CATRE_AFILIAT_VALUTA",
        "5124",
        "4511",
    ),
    (
        43,
        "RESTITUIRE_IMPRUMUT_CATRE_ASOCIAT_VALUTA",
        "RESTITUIRE_IMPRUMUT_CATRE_ASOCIAT_VALUTA",
        "4551",
        "5124",
    ),
    (
        45,
        "RESTITUIRE_MAJ_CAP_SOC_VALUTA",
        "RESTITUIRE_MAJ_CAP_SOC_VALUTA",
        "456",
        "5124",
    ),
    (168, "RETRAGERE_NUMERAR", "RETRAGERE_NUMERAR", "581", "5121"),
    (149, "SELL_BOND_BANCA", "SELL_BOND_BANCA", "5124", "506"),
    (5, "SELL_BOND_BROKER", "SELL_BOND_BROKER", "461", "506"),
    (
        137,
        "SELL_MONEY_MARKET",
        "Lichidare plasamente in fonduri de money market",
        "5124",
        "5081",
    ),
    (150, "SELL_STOCK_BANCA", "SELL_STOCK_BANCA", "5124", "5081"),
    (125, "SELL_STOCK_BROKER", "Vanzare acțiuni", "461", "5081"),
    (130, "SOLD_FINAL_RON", "SOLD_FINAL_RON", "5121", "5121"),
    (131, "SOLD_FINAL_VALUTA", "SOLD_FINAL_VALUTA", "5124", "5124"),
    (113, "SOLD_INITIAL_RON", "SOLD_INITIAL_RON", "5121", "5121"),
    (114, "SOLD_INITIAL_VALUTA", "SOLD_INITIAL_VALUTA", "5124", "5124"),
    (172, "STAMP_DUTY_BROKER", "STAMP_DUTY / TAXA ASF", "635", "461"),
    (
        112,
        "TRANSFER_INTERN_BROKER_IN",
        "TRANSFER_INTERN_BROKER_IN",
        "461",
        "581",
    ),
    (
        151,
        "TRANSFER_INTERN_BROKER_IN_BANCA",
        "TRANSFER_INTERN_BROKER_IN_BANCA",
        "5124",
        "581",
    ),
    (
        77,
        "TRANSFER_INTERN_BROKER_OUT",
        "VIR_INT_IN_BROKER_VALUTA",
        "581",
        "461",
    ),
    (
        160,
        "TRANSFER_INTERN_BROKER_OUT_BANCA",
        "TRANSFER_INTERN_BROKER_OUT_BANCA",
        "581",
        "5124",
    ),
    (36, "VIR_INT_IN_BANCA_LEI", "VIR_INT_IN_BANCA_LEI", "5121", "581"),
    (39, "VIR_INT_IN_BANCA_VALUTA", "VIR_INT_IN_BANCA_VALUTA", "5124", "581"),
    (111, "VIR_INT_IN_BROKER_IN", "VIR_INT_IN_BROKER_IN", "461", "581"),
    (
        98,
        "VIR_INT_IN_BROKER_VALUTA",
        "Depunere cont client la broker in valuta",
        "461",
        "581",
    ),
    (
        152,
        "VIR_INT_IN_BROKER_VALUTA_BANCA",
        "VIR_INT_IN_BROKER_VALUTA_BANCA",
        "5124",
        "581",
    ),
    (35, "VIR_INT_OUT_BANCA_LEI", "VIR_INT_OUT_BANCA_LEI", "581", "5121"),
    (41, "VIR_INT_OUT_BANCA_VALUTA", "VIR_INT_OUT_BANCA_VALUTA", "581", "5124"),
    (
        99,
        "VIR_INT_OUT_BROKER_VALUTA",
        "Retragere din cont broker in banca",
        "581",
        "461",
    ),
    (
        161,
        "VIR_INT_OUT_BROKER_VALUTA_BANCA",
        "VIR_INT_OUT_BROKER_VALUTA_BANCA",
        "581",
        "5124",
    ),
    (162, "WHT_BANCA", "WHT_BANCA", "635", "5124"),
    (9, "WHT_BROKER", "Impozit cu reținere la sursa broker", "635", "461"),
]

ACCOUNTING_ENTRIES = [
    # Full list of unique account codes and names needed by operations
    ("261", "Titluri de participare deținute la filiale din cadrul grupului"),
    ("265", "Alte titluri imobilizate"),
    ("401", "Furnizori"),
    ("4111", "Clienti"),
    ("4511", "Decontari intre entitatile afiliate"),
    ("4518", "Dobanzi aferente decontarilor intre entitati afiliate"),
    ("4551", "Actionari/ Asociati – conturi curente"),
    ("4558", "Actionari/ Asociati – dobanzi la conturi curente"),
    ("456", "Decontari cu acționarii/asociații privind capitalul"),
    ("461", "Debitori diversi"),
    ("462", "Creditori diversi"),
    ("473", "Operatiuni in curs de clarificare"),
    ("506", "Obligatiuni"),
    ("5081", "Alte titluri de plasament"),
    ("5088", "Dobânzi la obligatiuni si titluri de plasament"),
    ("5121", "Conturi la bănci in lei"),
    ("5124", "Conturi la bănci in valuta"),
    ("5125", "Sume in curs de decontare"),
    ("531", "CASA IN LEI"),
    ("581", "Viramente interne"),
    ("622", "Cheltuieli privind comisioanele si onorariile"),
    ("627", "Cheltuieli cu serviciile bancare si asimilate"),
    ("635", "Cheltuieli cu alte impozite, taxe și vărsăminte asimilate"),
    ("6642", "Cheltuieli privind investițiile financiare cedate"),
    ("6642DIF", "Dif curs - Cheltuieli privind investițiile financiare cedate"),
    (
        "6651",
        "Diferenţe nefavorabile de curs valutar legate de elementele monetare exprimate în valută",
    ),
    ("666", "Cheltuieli privind dobanzile"),
    ("7611", "Venituri din acțiuni deținute la entitățile afiliate"),
    ("762", "Venituri din investiții financiare"),
    ("7642", "Venituri din investiții financiare cedate"),
    ("7642DIF", "Dif curs - Venituri din investiții financiare cedate"),
    (
        "7651",
        "Diferenţe favorabile de curs valutar legate de elementele monetare exprimate în valută",
    ),
    ("766", "Venituri din dobânzi"),
]


class Command(BaseCommand):
    help = "Populates Accounting and Operation models"

    def handle(self, *args, **kwargs):
        acc_map = {}
        for code, name in ACCOUNTING_ENTRIES:
            acc_obj, created = Accounting.objects.get_or_create(
                account_code=code, defaults={"account_name": name}
            )
            acc_map[code] = acc_obj
            if created:
                self.stdout.write(
                    self.style.SUCCESS(f"Created Accounting {code} - {name}")
                )

        for op_id, code, name, debit_code, credit_code in OPERATIONS:
            debit = acc_map.get(debit_code)
            credit = acc_map.get(credit_code)

            if not debit or not credit:
                self.stdout.write(
                    self.style.WARNING(
                        f"Skipping {code} due to missing account"
                    )
                )
                continue

            op_obj, created = Operation.objects.get_or_create(
                operation_code=code,
                defaults={
                    "operation_name": name,
                    "debit": debit,
                    "credit": credit,
                },
            )
            if created:
                self.stdout.write(
                    self.style.SUCCESS(f"Created Operation {code} - {name}")
                )
            else:
                self.stdout.write(
                    self.style.NOTICE(f"Operation {code} already exists")
                )
