{
    // Python interpreter settings
    "python.defaultInterpreterPath": "./venv/Scripts/python.exe",
    "python.terminal.activateEnvironment": true,
    "python.terminal.activateEnvInCurrentTerminal": true,
    // Django settings
    "python.analysis.extraPaths": [
        "./",
        "./nch"
    ],
    "python.analysis.autoImportCompletions": true,
    "python.analysis.typeCheckingMode": "basic",
    // Django-specific settings
    "emmet.includeLanguages": {
        "django-html": "html"
    },
    "files.associations": {
        "*.html": "django-html",
        "**/*.html": "django-html"
    },
    // Code formatting
    "python.formatting.provider": "black",
    "python.formatting.blackArgs": [
        "--line-length=88"
    ],
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": false,
    "python.linting.flake8Enabled": true,
    "python.linting.flake8Args": [
        "--max-line-length=88",
        "--ignore=E203,W503"
    ],
    // Auto-save and formatting
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.organizeImports": "explicit"
    },
    // File exclusions for better performance
    "files.exclude": {
        "**/__pycache__": true,
        "**/*.pyc": true,
        "**/venv": false,
        "**/.git": true,
        "**/node_modules": true,
        "**/.logs": true,
        "**/static": true,
        "**/media": true
    },
    // Search exclusions
    "search.exclude": {
        "**/venv": true,
        "**/__pycache__": true,
        "**/*.pyc": true,
        "**/static": true,
        "**/media": true,
        "**/.logs": true
    },
    // Terminal settings
    "terminal.integrated.defaultProfile.windows": "PowerShell",
    "terminal.integrated.profiles.windows": {
        "PowerShell": {
            "source": "PowerShell",
            "icon": "terminal-powershell"
        },
        "Command Prompt": {
            "path": [
                "${env:windir}\\Sysnative\\cmd.exe",
                "${env:windir}\\System32\\cmd.exe"
            ],
            "args": [],
            "icon": "terminal-cmd"
        }
    },
    // Django template settings
    "django.snippets.exclude": [
        "cms",
        "wagtail"
    ],
    // IntelliSense settings
    "python.analysis.completeFunctionParens": true,
    "python.analysis.autoSearchPaths": true,
    // Debugging settings
    "python.testing.unittestEnabled": false,
    "python.testing.pytestEnabled": true,
    "python.testing.pytestArgs": [
        "."
    ],
    // Editor settings
    "editor.rulers": [
        88
    ],
    "editor.tabSize": 4,
    "editor.insertSpaces": true,
    "editor.detectIndentation": false,
    // Git settings
    "git.ignoreLimitWarning": true,
    // Workspace specific settings
    "workbench.colorCustomizations": {
        "titleBar.activeBackground": "#2d5a27",
        "titleBar.activeForeground": "#ffffff"
    }
}