# README_MANAGEMENT.md
# NCH Investment Management System - Management Commands Documentation

## Table of Contents

1. [Exchange Rate Management](#exchange-rate-management)
2. [Broker Data Import](#broker-data-import)
3. [Portfolio Management](#portfolio-management)
4. [Transaction Processing](#transaction-processing)
5. [Financial Calculations](#financial-calculations)
6. [Data Validation](#data-validation)
7. [Utility Commands](#utility-commands)
8. [Command Dependencies](#command-dependencies)
9. [Error Handling](#error-handling)
10. [Performance Considerations](#performance-considerations)

---

## Exchange Rate Management

### `curs_bnr.py`
**File**: `port/management/commands/curs_bnr.py`

#### Purpose
Downloads and imports Romanian National Bank (BNR) exchange rates into the database with proper historical tracking and business day logic.

#### Configuration Requirements
```python
# No special configuration required
# Uses public BNR XML API
```

#### Input Sources
- **Current Rates**: `https://bnr.ro/nbrfxrates10days.xml`
- **Historical Rates**: `https://www.bnr.ro/files/xml/years/nbrfxrates{year}.xml`

#### Processing Logic
```python
def get_bnr(url):
    """
    1. Fetch XML data from BNR API
    2. Parse XML to extract currency rates
    3. Handle multipliers for exotic currencies
    4. Create date mappings for business days
    5. Backfill missing dates with forward-fill logic
    """
    response = requests.get(url)
    my_dict = xmltodict.parse(response.content)['DataSet']['Body']['Cube']
    df = pd.DataFrame(my_dict).explode('Rate', ignore_index=True)
    
    # Process multipliers
    df['value'] = df['value'].astype(float)
    df.loc[~df['multiplier'].isna(),'value'] = df['value'] / df['multiplier'].astype(float)
    
    # Business day mapping
    bdays = sorted(df['date'].unique())
    nextbday = {bdays[i]: bdays[i+1] for i in range(len(bdays)-1)}
    df['date'] = df['date'].map(nextbday)
```

#### Database Operations
```python
# Bulk upsert with conflict resolution
unique = ['currency_code', 'date']
update_fields = unique + ['value', 'value_exact']
Bnr.objects.bulk_create(
    model_instances, 
    update_conflicts=True,
    unique_fields=unique,
    update_fields=update_fields,
)
```

#### Output
- **Database**: Updates `Bnr` table with latest rates
- **Coverage**: All major currencies (EUR, USD, GBP, etc.)
- **Frequency**: Daily execution via cron
- **Validation**: Automatic currency creation if missing

#### Error Handling
```python
# Network errors: Retry mechanism via cron
# Data errors: NaN handling with replace
# Missing currencies: Auto-creation in Currency table
bnr = bnr.replace(np.nan, None)
```

#### Usage
```bash
# Manual execution
python manage.py curs_bnr

# Cron schedule (twice daily with retry)
30 13 * * 1,2,3,4,5 python manage.py curs_bnr
30 15 * * 1,2,3,4,5 python manage.py curs_bnr
```

#### Performance Metrics
- **Execution Time**: 5-15 seconds
- **Data Volume**: ~50 rates per day
- **Memory Usage**: Minimal (<10MB)

---

## Broker Data Import

### `api_ibkr.py`
**File**: `brokers/management/commands/api_ibkr.py`

#### Purpose
Downloads Interactive Brokers Flex Query reports using the official IBKR API with automatic retry and timeout handling.

#### Configuration Requirements
```python
# Settings required
IBKR_TOKEN = os.environ.get('IBKR_TOKEN')
IBKR_QUERY = os.environ.get('IBKR_QUERY')

# File system paths
FILE_ROOT = settings.FILE_ROOT
BROKER = 'ibkr'
STATEMENTS_PATH = os.path.join(FILE_ROOT, BROKER, "raw")
```

#### API Integration
```python
class IbkrApi:
    BASE_URL = "https://ndcdyn.interactivebrokers.com/AccountManagement/FlexWebService"
    MAX_WAIT = 600  # 10 minutes timeout
    
    def flex_query(self, query_id: int):
        # Step 1: Request report generation
        url = f"{self.BASE_URL}/SendRequest?t={settings.IBKR_TOKEN}&q={query_id}&v=3"
        flex_req = requests.get(url=url)
        
        # Step 2: Parse response for reference code
        dict_data = xmltodict.parse(flex_req.text)['FlexStatementResponse']
        
        # Step 3: Poll for report completion
        request_url = f"{dict_data['Url']}?t={settings.IBKR_TOKEN}&q={dict_data['ReferenceCode']}&v=3"
        
        wait_time = 0
        while wait_time < self.MAX_WAIT:
            report = requests.get(url=request_url)
            res = xmltodict.parse(report.text)
            if 'FlexQueryResponse' in res:
                break
            wait_time += 1
```

#### Report Types Downloaded
- **OpenPositions**: Current portfolio holdings
- **CashReport**: Cash balances and flows
- **CashTransactions**: Cash movements
- **Trades**: Executed trades
- **CorporateActions**: Dividends, splits, etc.
- **SalesTaxes**: Transaction taxes
- **TransactionTaxes**: Stamp duties

#### Data Processing
```python
def xml_to_pandas(self):
    """
    Convert XML response to pandas DataFrames
    1. Parse JSON from saved XML
    2. Extract table structures
    3. Apply IBKR-specific formatting
    4. Save individual CSV files per table
    """
    @staticmethod
    def format_table(df):
        # Remove '@' from column names
        df.columns = df.columns.str.lstrip('@').str.lower()
        
        # Convert date columns
        date_columns = ['fromdate', 'reportdate', 'todate', 'datetime', 'tradedate']
        for col in date_columns:
            if col in df.columns:
                df[col] = pd.to_datetime(df[col], format='mixed')
```

#### Output Files
```
files/ibkr/raw/statements_YYYY-MM-DD.txt  # Raw JSON
files/ibkr/daily/OpenPositions_YYYY-MM-DD.csv
files/ibkr/daily/CashReport_YYYY-MM-DD.csv
files/ibkr/daily/Trades_YYYY-MM-DD.csv
# ... additional CSV files per report type
```

#### Error Handling
```python
# API errors
if dict_data['Status'] != 'Success':
    raise Exception(f"Error in Flex Query: {flex_req.text}")

# Timeout handling
if wait_time >= MAX_WAIT:
    raise TimeoutError("Timeout waiting for IBKR report")

# Network retry via cron scheduling
```

#### Usage
```bash
# Manual execution
python manage.py api_ibkr

# Cron schedule (twice daily)
15 8 * * 1,2,3,4,5,6 python manage.py api_ibkr
45 8 * * 1,2,3,4,5,6 python manage.py api_ibkr
```

### `api_tdv.py`
**File**: `brokers/management/commands/api_tdv.py`

#### Purpose
Downloads Tradeville data using WebSocket connection to the trading portal with multi-account support.

#### Configuration Requirements
```python
# Authentication settings
USER_TDV = os.environ.get('USER_TDV')
PASS_TDV = os.environ.get('PASS_TDV')

# Sub-account mapping
TDV_PERS = {
    'DD': os.environ.get('TDV_DD'),
    'AS': os.environ.get('TDV_AS'),
    'CP': os.environ.get('TDV_CP'),
}
```

#### WebSocket Implementation
```python
class MyWebSocket:
    def __init__(self):
        ws_link = "wss://portal.tradeville.ro/"
        headers = {
            'Origin': 'https://portal.tradeville.ro',
            'Sec-WebSocket-Protocol': 'pf4',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15)',
        }
        
        # Authentication message
        msg = {
            'cmd': 'login',
            'prm': {
                'coduser': settings.USER_TDV,
                'parola': settings.PASS_TDV,
                'demo': False,
            }
        }
        
        self.ws = websocket.create_connection(ws_link, 
            origin='https://portal.tradeville.ro',
            subprotocols=["pf4"],
            header=headers)
```

#### Data Extraction Methods
```python
def portof(self):
    """Get portfolio positions"""
    message = {
        'cmd': 'portof',
        'timeoutID': random.randint(1000,9999),
        'lagata': random.randint(1000,9999),
    }
    
def ordine(self):
    """Get order history"""
    message = {
        'cmd': 'ordine',
        'tinta': 'ordinePortal',
        'prm': {"dstart":'',"simbol":'',"stare":"%"}
    }
    
def activitate(self):
    """Get transaction history"""
    message = {
        'cmd': 'activit',
        'prm': {"dstart":'',"simbol":'',"dend":""}
    }
```

#### Multi-Account Processing
```python
def interogare(pers, functie):
    """Query each person/account separately"""
    df = pd.DataFrame()
    
    for firma in pers.keys():
        # Switch to specific sub-account
        msg = json.dumps({
            "cmd": "persoana",
            "tinta": "selectSubcont",
            'persoana': pers[firma],
        })
        result = tdv.send_msg(msg)
        
        # Execute query function
        dx = functie()
        if len(dx)>0:
            dx['firma'] = firma
            df = pd.concat([df, dx])
```

#### Output Files
```
files/tdv/portof_YYYY-MM-DD.csv     # Portfolio positions
files/tdv/ordine_YYYY-MM-DD.csv     # Order history  
files/tdv/activitate_YYYY-MM-DD.csv # Transaction history
```

#### Usage
```bash
# Manual execution
python manage.py api_tdv

# Cron schedule (twice daily)
20 7 * * 1,2,3,4,5,6 python manage.py api_tdv
30 15 * * 1,2,3,4,5,6 python manage.py api_tdv
```

---

## Portfolio Management

### `upload_ibkr_port.py`
**File**: `brokers/management/commands/upload_ibkr_port.py`

#### Purpose
Imports IBKR portfolio positions and instruments into the database with automatic instrument creation and position reconciliation.

#### Dependencies
- Requires `api_ibkr.py` to have run successfully
- Needs latest CSV files in `files/ibkr/daily/`

#### Processing Steps

##### 1. Data Import
```python
# Import relevant tables
tabs = ['OpenPositions', 'CashReport', 'SecuritiesInfo']

for t in tabs:
    list_of_files = glob.glob(root + t + '*') 
    latest_file = max(list_of_files, key=os.path.getctime)
    df = pd.read_csv(latest_file).dropna(axis=1, how='all')
    tab[t] = df
```

##### 2. Instrument Creation
```python
# Extract instrument information
rename = {
    'description': 'name',
    'assetcategory': 'type', 
    'subcategory': 'sector',
    'issuercountrycode': 'country',
}
cols = ['currency', 'symbol', 'isin', 'description', 'assetcategory']
secinfo = tab['SecuritiesInfo'][cols].rename(columns=rename)

# Find missing instruments
vals = secinfo['symbol'].unique()
all_vals = Instrument.objects.values_list('symbol', flat=True)
missing = set(vals) - set(all_vals)
```

##### 3. Portfolio Processing
```python
# Main portfolio positions
cols = ['accountid', 'symbol', 'reportdate', 'costbasismoney', 'positionvalue', 'position']
portfolio = tab['OpenPositions'][cols].rename(columns={
    'accountid': 'ubo',
    'reportdate': 'date',
    'costbasismoney': 'cost',
    'positionvalue': 'value', 
    'position': 'quantity',
})

# Add cash positions
cols = ['accountid', 'currency', 'fromdate', 'slbnetcash']
cash = tab['CashReport'][cols].rename(columns=rename)
cash['symbol'] = cash['currency']
cash['quantity'] = cash['value']
cash['cost'] = cash['value']

# Combine positions
portfolio = pd.concat([portfolio, cash])
```

##### 4. Database Upload
```python
model_instances = [
    Portfolio(
        ubo = Ubo.objects.get(ubo_code = row['ubo']),
        instrument = Instrument.objects.get(
            symbol = row['symbol'], custodian__custodian_code='IBKR'),
        date=row['date'],
        cost=round(row['cost'], 2),
        value=round(row['value'], 2),
        quantity=round(row['quantity'], 2),
        accruedint = row['accruedint'],
    ) for i, row in portfolio.iterrows()]

# Bulk upsert
unique = ['ubo', 'instrument', 'date']
update_fields = unique + ['cost', 'value', 'quantity', 'accruedint']
Portfolio.objects.bulk_create(
    model_instances, 
    update_conflicts=True,
    unique_fields=unique,
    update_fields=update_fields,
)
```

#### Validation
- Checks for missing instruments before portfolio import
- Validates UBO mapping (********* -> DD)
- Reports duplicate entries

#### Usage
```bash
python manage.py upload_ibkr_port

# Automated via cron after api_ibkr
15 9 * * 1,2,3,4,5,6 python manage.py upload_ibkr_port
```

### `upload_tdv_port.py`
**File**: `brokers/management/commands/upload_tdv_port.py`

#### Purpose
Imports Tradeville portfolio positions with multi-entity support and currency handling.

#### Processing Logic

##### 1. Data Import and Cleaning
```python
# Import portfolio data
list_of_files = glob.glob(root + 'portof*') 
latest_file = max(list_of_files, key=os.path.getctime)
df = pd.read_csv(latest_file, na_filter=False).dropna(axis=1, how='all')

# Clean empty columns
for c in df.columns:
    uni = list(df[c].unique())
    if len(uni)==0: 
        df.drop(columns=[c], inplace=True)
```

##### 2. Instrument Processing
```python
cols = {
    'valuta': 'currency',
    'simbol': 'symbol', 
    'isin': 'isin',
    'nume': 'name',
    'firma': 'ubo',
}
secinfo = tab['portof'].rename(columns=cols)[cols.values()]

# Find missing instruments
vals = secinfo['symbol'].unique()
all_vals = Instrument.objects.values_list('symbol', flat=True)
missing = list(set(vals) - set(all_vals))
```

##### 3. Portfolio Calculation
```python
portfolio = tab['portof'].rename(columns=cols)[cols.values()]
portfolio['cost'] = portfolio['unit_cost'] * portfolio['quantity']
portfolio['value'] = portfolio['value'] * portfolio['quantity']

# Special handling for cash positions
CURRENCIES = ['EUR', 'USD', 'RON', 'GBP', 'CAD', 'SEK']
portfolio.loc[portfolio['symbol'].isin(CURRENCIES), 'cost'] = 1
```

##### 4. Database Operations
```python
# Create instrument instances
model_instances = [
    Instrument(
        currency = Currency.objects.get(currency_code = row['currency']),
        custodian = Custodian.objects.get(custodian_code = 'TDV'),
        symbol = row['symbol'],
        isin=row['isin'],
        name=row['name'],
        type='PLEASE UPDATE',
        sector='PLEASE UPDATE', 
        country='PLEASE UPDATE',
    ) for i, row in secinfo.iterrows()]

# Portfolio instances
model_instances = [
    Portfolio(
        ubo = Ubo.objects.get(ubo_code = row['ubo']),
        instrument = Instrument.objects.get(symbol=row['symbol'], custodian__custodian_code='TDV'),
        date=today,
        cost=round(row['cost'], 2),
        value=round(row['value'], 2),
        quantity=round(row['quantity'], 2),
    ) for i, row in portfolio.iterrows()]
```

#### Features
- **Multi-Entity Support**: Handles multiple UBOs (DD, AS, CP)
- **Currency Detection**: Automatic cash position handling
- **Lock Date Awareness**: Respects period locking
- **Error Reporting**: Missing instrument detection

#### Usage
```bash
python manage.py upload_tdv_port

# Automated execution
40 7 * * 1,2,3,4,5,6 python manage.py upload_tdv_port
50 15 * * 1,2,3,4,5,6 python manage.py upload_tdv_port
```

---

## Transaction Processing

### `uptrz_ibkr.py`
**File**: `brokers/management/commands/uptrz_ibkr.py`

#### Purpose
Generates accounting journal entries from IBKR transaction data with sophisticated cost basis calculation, FX handling, and reconciliation.

#### Configuration
```python
broker = 'ibkr'
custodian_code = 'IBKR'
UBO = 'DD'
UPLOAD = False  # Safety flag for database upload
```

#### Data Import and Processing

##### 1. Import Multiple Report Types
```python
tabs = [
    'OpenPositions',   # Current positions for reconciliation
    'CashReport',      # Cash flows and balances
    'CashTransactions', # Cash movements
    'CorporateActions', # Dividends, splits
    'Trades',          # Executed trades
    'SalesTaxes',      # VAT and similar taxes
    'TransactionTaxes', # Stamp duties
]

for t in tabs:
    list_of_files = glob.glob(root + t + '*') 
    latest_file = max(list_of_files, key=os.path.getctime)
    df = pd.read_csv(latest_file).dropna(axis=1, how='all')
    tab[t] = df
```

##### 2. Transaction Classification and Splitting

###### Cash Transactions
```python
cols = ['transactionid', 'type', 'currency', 'assetcategory', 'symbol', 'isin', 
        'datetime', 'amount', 'description']
tr_cash = tab['CashTransactions'][cols].copy()
tr_cash.rename(columns={
    'datetime': 'date', 
    'amount': 'value',
    'description': 'details',
    'type': 'operation',
}, inplace=True)
```

###### Trades Processing
```python
# Main trade entries
trades = tab['Trades'][cols].copy()
trades.rename(columns={
    'transactiontype': 'operation', 
    'tradedate': 'date',
    'fifopnlrealized': 'profit',
}, inplace=True)

# Split commissions into separate entries
trades_comm = trades[trades['ibcommission']!=0].copy()
trades_comm['operation'] = 'COMIS_BROKER_VALUTA'
trades_comm['value'] = trades_comm['ibcommission']
trades_comm['quantity'] = 0

# Handle FX trades separately
trades_fxin = trades[trades['assetcategory']=='CASH'].copy()
trades_fxin['operation'] = 'FX_IN_BROKER'
trades_fxin['symbol'] = trades_fxin['currency']
trades_fxin['value'] = trades_fxin['proceeds']

trades_fxout = trades[trades['assetcategory']=='CASH'].copy()
trades_fxout['operation'] = 'FX_OUT_BROKER'
trades_fxout['symbol'] = trades_fxout['symbol'].str[:3]
trades_fxout['value'] = trades_fxout['quantity']
```

##### 3. Cost Basis Calculation (Weighted Average)
```python
# Separate trades from other operations
TRADES = ['BUY_BOND', 'SELL_BOND']
all_x = all[~(all['operation'].isin(TRADES))].copy()
trades = all[all['operation'].isin(TRADES)].copy()

# Calculate weighted average cost per symbol
df = pd.DataFrame()
for symbol in trades['symbol'].unique():
    dx = trades[trades['symbol']==symbol].reset_index(drop=True).copy()
    dx[['value_at_cost','unit_cost']] = [0.0, 0.0]
    
    latest_cost = 0
    cumulated_value = 0
    cumulated_quantity = 0

    for index, row in dx.iterrows():
        if row['quantity'] >= 0:  # Buy
            dx.loc[index, 'value_at_cost'] = -row['value']
            cumulated_value += -row['value']
            cumulated_quantity += row['quantity']
            latest_cost = cumulated_value / cumulated_quantity
            dx.loc[index, 'unit_cost'] = latest_cost
        else:  # Sell
            cost = row['quantity'] * latest_cost
            dx.loc[index, 'value_at_cost'] = cost
            cumulated_quantity += row['quantity']
            cumulated_value += cost
            
            if cumulated_quantity > 0:
                latest_cost = cumulated_value / cumulated_quantity
            else:
                latest_cost = 0
            dx.loc[index, 'unit_cost'] = latest_cost
                
    dx['profit'] = dx['value'] + dx['value_at_cost']
    df = pd.concat([df, dx], ignore_index=True)
```

##### 4. Reconciliation

###### Position Reconciliation
```python
recon = all.pivot_table(index=['isin'], values=['quantity'], aggfunc='sum').reset_index()
recon = recon.merge(positions[['isin', 'position']], how='outer', on='isin')
recon['reconcile'] = recon['quantity'] - recon['position']

if max(recon['reconcile'])==min(recon['reconcile']):
    print('Position reconciliation OK')
else:
    print('Position reconciliation ERROR')
```

###### Cash Reconciliation
```python
for currency in all['currency'].unique():
    cash_end = cash[cash['currency']==currency]['endingcash'].sum()
    cash_calc = all[all['currency']==currency]['value'].sum()
    diff = cash_end - cash_calc
    
    if abs(diff)<=0.01:
        print('Cash reconciliation OK', currency)
    else:
        print('Cash reconciliation ERROR', currency, diff)
```

##### 5. Operation Reclassification
```python
reclas = {
    'DEPOSITS/WITHDRAWALS': 'VIRAMENT',
    'BOND INTEREST PAID': 'BOND_INTEREST_PAID_BROKER',
    'BOND INTEREST RECEIVED': 'BOND_INTEREST_RECEIVED_BROKER',
    'WITHHOLDING TAX': 'WHT_BROKER',
    'BROKER INTEREST PAID': 'BROKER_INTEREST_PAID_BROKER',
    'BROKER INTEREST RECEIVED': 'BROKER_INTEREST_RECEIVED',
    'OTHER FEES': 'COMIS_BROKER_VALUTA',
    'BUY': 'BUY_BOND_BROKER',
    'SELL': 'SELL_BOND_BANCA',
    'DIVIDENDS': 'INC_DIVIDEND_BROKER',
}
all.loc[all['operation'].isin(reclas.keys()), 'operation'] = all['operation'].map(reclas)
```

##### 6. Unique Transaction ID Generation
```python
def make_transaction_ids_unique(df):
    """Make transaction IDs unique by adding incremental counters"""
    is_duplicate = df.duplicated('transactionid', keep='first')
    
    if is_duplicate.any():
        dup_counts = df[is_duplicate].groupby('transactionid').cumcount()
        df.loc[is_duplicate, 'transactionid'] = (
            df.loc[is_duplicate, 'transactionid'].astype(str) + 
            '.*' + 
            (dup_counts + 1).astype(str)
        )
    return df
```

#### Output
- **Excel File**: `files/reports/journal_ibkr_YYYY-MM-DD.xlsx`
- **Database**: Journal entries (when UPLOAD=True)
- **Reconciliation**: Automatic validation reports

#### Validation Checks
1. **Missing Operations**: Checks for unmapped operation codes
2. **Missing Instruments**: Validates all symbols exist
3. **Position Reconciliation**: Compares calculated vs. reported positions
4. **Cash Reconciliation**: Validates cash flows per currency

#### Usage
```bash
# Generate journal (review mode)
python manage.py uptrz_ibkr

# Upload to database (set UPLOAD=True in code)
python manage.py uptrz_ibkr
```

### `uptrz_tdv.py`
**File**: `brokers/management/commands/uptrz_tdv.py`

#### Purpose
Processes Tradeville transaction data into accounting journal entries with transaction splitting, reconciliation, and multi-entity support.

#### Processing Architecture

##### 1. Data Import and Validation
```python
broker = 'tdv'
UBO = ['DD', 'AS']
CURRENCIES = ['EUR', 'USD', 'RON', 'GBP', 'CAD', 'SEK']

# Import activity and portfolio data
for t in ['activitate', 'portof']:
    list_of_files = glob.glob(root + t + '*') 
    latest_file = max(list_of_files, key=os.path.getctime)
    df = pd.read_csv(latest_file, na_filter=False)
    tab[t] = df
```

##### 2. Transaction Decomposition

###### Main Transaction Processing
```python
cols = {
    'id': 'transactionid',
    'op': 'operation',
    'simbol': 'symbol',
    'data': 'date',
    'firma': 'ubo',
    'suma': 'valuex',
    'cant': 'quantity',
    'descr': 'details',
    'valuta': 'currency',
    'costm': 'unit_cost',
    'pret': 'price', 
    'dirty': 'dirty',      # Price including accrued interest
    'comis': 'comis',      # Commission (to be split)
    'txcnlei': 'txcnlei',  # CNVM tax (to be split)
    'profit': 'profit',    # Realized profit (to be split)
}

df = tab['activitate'].rename(columns=cols)[cols.values()].copy()

# Sign adjustment for operations
out_cash = ['cump', 'out']
df.loc[df['operation'].isin(out_cash), 'price'] = -df['price']
df.loc[df['operation'].isin(['out']), 'quantity'] = -df['quantity']
```

###### Interest Separation
```python
# Separate accrued interest from clean price
col = 'interest'
df[col] = 0.0
df.loc[df['dirty']!=0.0, col] = df['quantity'] * (df['dirty'] - df['price'])
df.loc[df['dirty']!=0.0, 'value'] = df['quantity'] * df['price']

# Create separate entries for interest
dfx = df[df[col]!=0].copy()
dfx['value'] = dfx[col]
dfx['quantity'] = 0.0
dfx.loc[dfx['operation'].isin(['cump']), 'operation'] = 'BOND_INTEREST_PAID_BROKER'
dfx.loc[dfx['operation'].isin(['vanz']), 'operation'] = 'BOND_INTEREST_RECEIVED_BROKER'

journal = pd.concat([journal, dfx], ignore_index=True)
```

###### Commission Separation
```python
# Separate commission entries
col = 'comis'
dfx = df[df[col]!=0].copy()
dfx['value'] = -dfx[col]
dfx['quantity'] = 0.0
dfx['operation'] = 'COMIS_BROKER_VALUTA'

journal = pd.concat([journal, dfx], ignore_index=True)
```

###### Tax Separation
```python
# Separate CNVM tax entries
col = 'txcnlei'
dfx = df[df[col]!=0.0].copy()
dfx['value'] = -dfx[col]
dfx['operation'] = 'COMIS_BROKER_VALUTA'
dfx['quantity'] = 0.0

# Adjust main entry value by removing tax
journal['value'] = journal['value'] - journal[col]
journal = pd.concat([journal, dfx], ignore_index=True)
```

##### 3. Reconciliation Logic

###### Quantity Reconciliation
```python
# Current positions (excluding cash)
df_now = portfolio[['symbol', 'quantity', 'ubo']].rename(columns={'quantity': 'quantity_real'})
df_now = df_now[~df_now['symbol'].isin(CURRENCIES)]

# Calculated positions from transactions
df_recon = journal.pivot_table(index=['symbol', 'ubo'], values=['quantity'], aggfunc='sum').reset_index()

# Compare actual vs calculated
df_recon = df_recon.merge(df_now, how='outer', on=['symbol', 'ubo'])
df_recon['diff'] = df_recon['quantity_real'] - df_recon['quantity']

if (abs(min(df_recon['diff']))>=0.01) | (abs(max(df_recon['diff']))>=0.01):
    print('Quantity reconciliation ERROR')
    print(df_recon)
else:
    print('Quantity reconciliation OK')
```

###### Cash Reconciliation
```python
# Current cash positions
df_now = portfolio[portfolio['symbol'].isin(CURRENCIES)][['symbol', 'value', 'ubo']]
df_now = df_now.pivot_table(index=['currency', 'ubo'], values=['value_real'], aggfunc='sum').reset_index()

# Calculated cash from transactions
df_recon = journal.pivot_table(index=['currency', 'ubo'], values=['value'], aggfunc='sum').reset_index()

# Compare and validate
df_recon = df_recon.merge(df_now, how='outer', on=['currency', 'ubo'])
df_recon['diff'] = df_recon['value'] - df_recon['value_real']

if (abs(min(df_recon['diff']))>=0.02) | (abs(max(df_recon['diff']))>=0.02):
    print('Cash reconciliation ERROR')
else:
    print('Cash reconciliation OK')
```

##### 4. Transaction Classification

###### Operation Mapping
```python
# Standard operation renaming
journal.loc[journal['operation']=='cump', 'operation'] = 'BUY_BOND_BROKER'
journal.loc[journal['operation']=='vanz', 'operation'] = 'SELL_BOND_BROKER'
journal.loc[journal['operation']=='div', 'operation'] = 'BOND_INTEREST_RECEIVED_BROKER'
```

###### Account Assignment
```python
# Assign account based on currency and account type
journal['account'] = 'TDV_' + journal['currency']

# Special handling for EUR accounts
journal.loc[
    (journal['currency']=='EUR') 
    & (~journal['details'].str.contains('.')), 'account'
] = 'TDV_RE'
```

###### Transaction Type Recognition
```python
# Internal transfers between accounts
re_eur = ['TRANSFER DIN D6DN39-RE IN D6DN39.UE', 'TRANSFER DIN RE IN UE']
journal.loc[
    (journal['operation'].isin(['in', 'out']))
    & (journal['details'].isin(re_eur)), 'operation'
] = 'TRANSFER_INTERN_BROKER'

# Deposits from external sources
journal.loc[
    (journal['operation']=='in')
    & (journal['details'].str.startswith('DEPUNERE')), 'operation'
] = 'VIR_INT_IN_BROKER_VALUTA'

# Withdrawals to external accounts
journal.loc[
    (journal['operation']=='out')
    & (journal['details'].str.startswith('RETRAGERE')), 'operation'
] = 'VIR_INT_OUT_BROKER_VALUTA'

# FX transactions
fx_in = ['TRANSFER DIN D6DN39.UE IN D6DN39.US']
journal.loc[
    (journal['operation']=='in')
    & (journal['details'].isin(fx_in)), 'operation'
] = 'FX_IN'

fx_out = 'USD IN US CURS'
journal.loc[
    (journal['operation']=='out')
    & (journal['details'].str.contains(fx_out)), 'operation'
] = 'FX_OUT'
```

#### Output Generation
```python
# Save to Excel for review
today = str(datetime.today())[:10]
save_file = settings.FILE_ROOT + f"reports/journal_tdv_{today}.xlsx"
journal.to_excel(save_file, index=False)

# Database upload preparation (commented out for safety)
# model_instances = [Journal(...) for row in journal.iterrows()]
# Journal.objects.bulk_create(model_instances, ...)
```

#### Validation Reports
1. **Missing Operations**: Lists unmapped operation codes
2. **Missing Instruments**: Identifies missing symbols
3. **Quantity Reconciliation**: Position validation
4. **Cash Reconciliation**: Cash flow validation

#### Usage
```bash
# Generate TDV journal
python manage.py uptrz_tdv

# Output location
files/reports/journal_tdv_YYYY-MM-DD.xlsx
```

### `uptrz_manual.py`
**File**: `brokers/management/commands/uptrz_manual.py`

#### Purpose
Imports manually prepared journal entries from Excel files with foreign key validation and cost basis calculation.

#### Supported Sources
```python
tabs = [
    # 'journal_banks',  # Bank statement entries (disabled)
    'journal_mst',      # Morgan Stanley entries
]
```

#### Processing Workflow

##### 1. File Import
```python
all = pd.DataFrame()
root = settings.FILE_ROOT + 'reports/'

for t in tabs:
    list_of_files = glob.glob(root + t + '*.xlsx') 
    latest_file = max(list_of_files, key=os.path.getctime)
    df = pd.read_excel(latest_file)
    all = pd.concat([all, df], ignore_index=True)
```

##### 2. Foreign Key Validation and Creation

###### Operation Codes
```python
# Create missing operations (commented for safety)
# for x in all['operation'].unique():
#     operationid = Operation.objects.filter(operation_code=x).all().values()
#     if len(operationid)==0:
#         obj = Operation(
#             operation_code = x,
#             operation_name = x,
#         )
#         obj.save()
```

###### Account Creation
```python
# Create missing accounts
df = all.drop_duplicates(subset=['account'], keep='first')
model_instances = [
    Account(
        currency_code = Currency.objects.get(currency_code = row['currency']),
        ubo_code = Ubo.objects.get(ubo_code = row['ubo']),
        custodian_code = Custodian.objects.get(custodian_code = row['custodian']),
        account_code = row['account'],
        account_name = row['account']
    ) for i, row in df.iterrows()]

Account.objects.bulk_create(model_instances, ignore_conflicts=True)
```

###### Partner Creation
```python
df = all.drop_duplicates(subset=['partner'], keep='first')
model_instances = [
    Partner(
        partner_type = Partner_type.objects.get(partner_type_code = 'UNDEFINED'),
        partner_code = row['partner'],
        partner_name = row['partner'],
    ) for i, row in df.iterrows()]

Partner.objects.bulk_create(model_instances, ignore_conflicts=True)
```

###### Instrument Creation
```python
df = all.drop_duplicates(subset=['symbol', 'custodian'], keep='first')
model_instances = [
    Instrument(
        currency = Currency.objects.get(currency_code = row['currency']),
        custodian = Custodian.objects.get(custodian_code = row['custodian']),
        symbol = row['symbol'],
        isin = 'UNDEFINED',
        name = row['symbol'],
    ) for i, row in df.iterrows()]

Instrument.objects.bulk_create(model_instances, ignore_conflicts=True)
```

##### 3. Cost Basis Calculation (Commented Template)
```python
# Template for weighted average cost calculation
# TRADES = ['BUY', 'SELL']
# all_x = all[~(all['operation'].isin(TRADES))].copy()
# trades = all[all['operation'].isin(TRADES)].copy()

# for symbol in trades['symbol'].unique():
#     dx = trades[trades['symbol']==symbol].reset_index(drop=True).copy()
#     # ... weighted average cost calculation logic
```

##### 4. Database Upload
```python
model_instances = [
    Journal(
        ubo = Ubo.objects.get(ubo_code = row['ubo']),
        custodian = Custodian.objects.get(custodian_code = row['custodian']),
        account = Account.objects.get(account_code = row['account']),
        operation = Operation.objects.get(operation_code = row['operation']),
        partner = Partner.objects.get(partner_code = row['partner']),
        instrument = Instrument.objects.get(symbol=row['symbol'], custodian__custodian_code=row['custodian']),
        date = row['date'],
        transactionid = row['transactionid'],
        value = row['value'],
        quantity = row['quantity'],
        details = row['details'],
        unit_cost = row['unit_cost'],
        profit = row['profit'],
    ) for i, row in all.iterrows()]

# Bulk upsert
unique = ['ubo', 'custodian', 'account', 'transactionid', 'operation']
update_fields = unique + ['partner', 'instrument', 'date', 'value', 'quantity', 'details', 'unit_cost', 'profit', 'value_gross']

Journal.objects.bulk_create(
    model_instances,
    update_conflicts=True,
    unique_fields=unique,
    update_fields=update_fields,
)
```

#### Expected Excel Format
```
Columns required:
- ubo: Entity code (DD, AS, etc.)
- custodian: Broker/bank code
- account: Account identifier
- operation: Operation code
- partner: Counterparty code
- symbol: Instrument symbol
- currency: Currency code
- date: Transaction date
- transactionid: Unique transaction ID
- value: Transaction value
- quantity: Quantity traded
- details: Description
- unit_cost: Cost per unit
- profit: Realized profit
- value_gross: Gross value
```

#### Usage
```bash
# Import manual journal entries
python manage.py uptrz_manual

# Expected file location
files/reports/journal_mst_*.xlsx
```

---

## Financial Calculations

### `bond_accruals_ql.py`
**File**: `port/management/commands/bond_accruals_ql.py`

#### Purpose
Calculates bond interest accruals using QuantLib library with professional-grade bond mathematics, multiple day count conventions, and FX revaluation.

#### Dependencies
```python
import QuantLib as ql
from .lib.bondlib import EnhancedBondAccrualCalculator
```

#### Configuration
```python
class EnhancedBondAccrualCalculator:
    paymentConvention = {
        '30/360': ql.Thirty360(ql.Thirty360.ISDA),
        'ISMA-30/360': ql.Thirty360(ql.Thirty360.European),
        'ACT/ACT': ql.ActualActual(ql.ActualActual.ISMA),
    }
    
    calendar_map = {
        'GovernmentBond': ql.UnitedStates(ql.UnitedStates.GovernmentBond),
        'TARGET': ql.TARGET(),
        'NYSE': ql.UnitedStates(ql.UnitedStates.NYSE),
        'FederalReserve': ql.UnitedStates(ql.UnitedStates.FederalReserve),
        'Settlement': ql.UnitedStates(ql.UnitedStates.Settlement),
    }
```

#### Data Retrieval
```python
def query_data(self):
    # 1. Journal entries for bonds
    journal_qs = Journal.objects.filter(
        operation__operation_code__contains='BOND'
    ).select_related('operation', 'instrument', 'instrument__currency')
    
    # 2. BNR exchange rates with end-of-month rates
    bnr_qs = Bnr.objects.filter(
        currency_code__currency_code__in=['EUR', 'USD', 'MXN']
    )
    
    # 3. Bond instrument definitions
    instruments_qs = Instrument.objects.filter(type='BOND')
```

#### Bond Calculation Process

##### 1. Coupon Schedule Generation
```python
def get_coupon_schedule(self, bond_info):
    """Generate coupon payment schedule using QuantLib"""
    calendar = self.calendar_map.get(bond_info['calendar'], self.calendar_map['TARGET'])
    
    frequency = ql.Annual
    if bond_info['bond_coupon_count'] == 2:
        frequency = ql.Semiannual

    schedule_coupon = ql.Schedule(
        issue_date,
        maturity_date,
        ql.Period(frequency),
        calendar,
        ql.Following,    # Payment convention
        ql.Following,    # Termination convention
        ql.DateGeneration.Forward,
        False,           # End of month rule
        first_coupon,    # First coupon date
    )
    
    return schedule_coupon
```

##### 2. Accrual Calculation
```python
def calculate_accruals(self, date, quantity, bond_info, prev_coupon_date=None):
    """Calculate accrued interest using QuantLib"""
    
    # Settlement date calculation
    settlement_days = 2
    new_settlement_cutoff_date = ql.Date(28, 5, 2024)
    if (bond_info['currency'] == 'USD') and (trade_date > new_settlement_cutoff_date):
        settlement_days = 1
    
    # Calendar adjustment
    calendar = self.calendar_map.get(bond_info['calendar'])
    settlement_date = calendar.advance(
        trade_date,
        ql.Period(settlement_days, ql.Days),
        ql.Following
    )
    
    # Handle coupon payment dates
    if settlement_date in coupon_dates:
        settlement_date = trade_date
    
    # Create QuantLib bond
    coupon_rate = bond_info['interest'] / 100.0
    payment_convention = self.paymentConvention.get(bond_info['convention'])
    
    bond = ql.FixedRateBond(
        settlement_days,
        100,                    # Face amount
        schedule_coupon,
        [coupon_rate],
        payment_convention
    )
    
    # Calculate accrued interest
    ql.Settings.instance().evaluationDate = settlement_date
    accrued_percentage = bond.accruedAmount(settlement_date)
    accrued_amount = (accrued_percentage / 100.0) * quantity
    
    return accrued_amount
```

##### 3. Processing Workflow
```python
for symbol in df_bond_info['symbol'].unique():
    # Get bond transactions
    dx = df_bonds[df_bonds['symbol'] == symbol].copy()
    bond_info = df_bond_info[df_bond_info['symbol']==symbol].to_dict('records')[0]
    
    # Multiply quantity by face value
    dx['quantity'] = dx['quantity'] * bond_info['face_value']
    
    # Generate accrual dates (end of month)
    start_date = dx['date'].min()
    end_date = min(datetime.today().date(), bond_info['maturity'])
    accrual_days = [
        (start_date.replace(day=1) + relativedelta(months=i+1) - relativedelta(days=1))
        for i in range((end_date.year - start_date.year) * 12 + end_date.month - start_date.month + 1)
    ]
    
    # Add transaction dates for intermediate accruals
    other_accrual_dates = sorted(list(set(dx['date'].unique()) - set(accrual_days)))
```

##### 4. Coupon Payment Handling
```python
# Get all coupon dates
coupon_dates = list(self.calculator.get_coupon_schedule(bond_info))
coupon_dates = [date(d.year(), d.month(), d.dayOfMonth()) for d in coupon_dates]

# Correct coupon payment dates using merge_asof
dx_coupon = dx[dx['operation']=='BOND_COUPON_RECEIVED_BROKER'].copy()
dx_coupon_dates = pd.DataFrame({'date': coupon_dates, 'real_date': coupon_dates})

dx_coupon = pd.merge_asof(dx_coupon, dx_coupon_dates, 
    on='date', direction='nearest',
    tolerance=pd.Timedelta(days=5),
    allow_exact_matches=True)
```

##### 5. FX Revaluation
```python
# Calculate accruals in original currency
dx['accruals_total'] = dx.apply(lambda row: self.calculator.calculate_accruals(
    row['date'], row['quantity_total'], bond_info), axis=1)

# Add BNR rates
dx = pd.merge_asof(left=dx, right=df_bnr, on='date', by=['currency'])
dx = pd.merge_asof(left=dx, right=df_bnr_eom, on='date', by=['currency'])

# Calculate incremental accruals
dac = dx[dx['operation']=='INTEREST_ACCRUAL_BOND'].copy()
dac['accrual_incremental'] = dac['accruals_total'] - dac['accruals_total'].shift(1).fillna(0)

# FX difference calculation
dfx = dx[dx['operation'].isin(['INTEREST_ACCRUAL_BOND', 'BOND_COUPON_RECEIVED_BROKER'])].copy()
dfx['revalue_total'] = (dfx['total_accrual_valuta'] * dfx['bnr']).round(2)
dfx.loc[dfx['revalue'], 'revalue_total'] = (dfx['total_accrual_valuta'] * dfx['bnr_eom']).round(2)
dfx['fx_diff'] = dfx['total_fx_diff'] - dfx['total_fx_diff'].shift(1).fillna(0)
```

#### Output Generation
```python
# Export calculations
save_path = settings.FILE_ROOT + f"accruals/bond_accruals_{today}.xlsx"

with pd.ExcelWriter(save_path) as writer:
    df_bonds.to_excel(writer, sheet_name='All_Bonds', index=False)
    df_bnr.to_excel(writer, sheet_name='BNR_Rates', index=False)
    df_bond_info.to_excel(writer, sheet_name='Bond_Definitions', index=False)
    df_all.to_excel(writer, sheet_name='CALCULE', index=False)
    df_all_export.to_excel(writer, sheet_name='EXPORT', index=False)

# CSV export for database import
save_path_csv = settings.FILE_ROOT + f"accruals/bond_accruals_export_{today}.csv"
df_all_export.to_csv(save_path_csv, index=False)
```

#### Journal Entry Generation
```python
# Generate transaction IDs
df_all_export['transactionid'] = (
    df_all_export['symbol'] + ' ' + 
    df_all_export['op_symbol'] + ' ' + 
    df_all_export['date'].astype(str)
)

# Query existing journal IDs for updates
journal_map = pd.DataFrame(Journal.objects.all().values('id', 'transactionid'))
df_all_export = df_all_export.merge(journal_map, on='transactionid', how='left')
```

#### Usage
```bash
# Calculate bond accruals
python manage.py bond_accruals_ql

# Output files
files/accruals/bond_accruals_YYYY-MM-DD.xlsx
files/accruals/bond_accruals_export_YYYY-MM-DD.csv
```

### `deposit_accruals.py`
**File**: `port/management/commands/deposit_accruals.py`

#### Purpose
Calculates interest accruals for bank deposits with multiple day count conventions, FX revaluation, and maturity processing.

#### Data Sources
```python
# 1. BNR exchange rates
df_bnr = pd.DataFrame(
    Bnr.objects.select_related('currency_code')
    .filter(value_exact__isnull=False)
    .values('currency_code__currency_code', 'date', 'value_exact')
)

# 2. Deposit definitions
deposits = Deposits.objects.select_related('deposit__currency', 'deposit__custodian')
    .values('principal', 'interest_rate', 'convention', 'start', 'maturity', 
           'deposit__currency__currency_code', 'deposit__custodian__custodian_code', 
           'deposit__symbol', 'interest_amount', 'new_deposit', 'liquidated')
    .filter(maturity__gt='2024-01-31')
```

#### Calculation Engine
```python
def calculate_accruals_fx(self, inputs, df_bnr):
    """Main accrual calculation function"""
    
    # Extract deposit parameters
    principal = inputs['principal']
    interest_rate = inputs['interest_rate'] / 100
    convention = inputs['convention']
    start_date = pd.Timestamp(inputs['start_date'])
    end_date = pd.Timestamp(inputs['end_date'])
    currency = inputs['currency']
    
    # Calculate end-of-month accrual dates
    max_date = pd.Timestamp.today().replace(day=1)
    end_of_months = pd.date_range(
        start=start_date,
        end=min(max_date, end_date),
        freq='ME'
    )
```

##### 1. Initial Deposit Entry
```python
# Initial deposit at start
initial_bnr_rate = bnr[bnr['date']<start_date]['rate'].iloc[0]
operation_code = 'CONSTITUIRE_DEP_VALUTA' if currency != 'RON' else 'CONSTITUIRE_DEP_LEI'

results.append({
    'date': str(start_date)[:10],
    'operation': operation_code,
    'value': -principal,
    'currency': currency,
    'bnr': initial_bnr_rate,
    'value_ron': -principal * initial_bnr_rate,
    'quantity': principal,
    'transactionid': f"{deposit_id} CONSTITUIRE {str(start_date)[:10]}",
    'idx': 'ADAUGA' if prima_constituire else None,
})
```

##### 2. Monthly Accrual Calculation
```python
# Monthly interest accrual
for date in end_of_months:
    accrual_days = (date - last_accrual_date).days
    
    # Get BNR rates
    bnr_rate = bnr[bnr['date']<date]['rate'].iloc[0]
    bnr_rate_end = bnr[bnr['date']<=date]['rate'].iloc[0]
    
    # Calculate accrued interest
    accrued_interest = (principal * interest_rate * accrual_days) / convention
    accrued_interest_ron = accrued_interest * bnr_rate
    
    # Accumulate
    accrued_interest_cumulative += accrued_interest
    accrued_interest_cumulative_ron += accrued_interest_ron
    
    # Create accrual entry
    operation_code = 'ACCRUAL_INTEREST_VALUTA' if currency != 'RON' else 'ACCRUAL_INTEREST_LEI'
    results.append({
        'date': str(date)[:10],
        'operation': operation_code,
        'value': accrued_interest,
        'cumulative_interest': accrued_interest_cumulative,
        'currency': currency,
        'bnr': bnr_rate,
        'value_ron': accrued_interest_ron,
        'transactionid': f"{deposit_id} ACC {str(date)[:10]}",
    })
```

##### 3. FX Difference Calculation
```python
if currency != 'RON':
    # FX difference on principal
    fx_diff_principal = principal * (bnr_rate_end - last_bnr_rate_end)
    operation = 'FX_DIF_DEP_PLUS' if fx_diff_principal >= 0 else 'FX_DIF_DEP_MINUS'
    
    if abs(fx_diff_principal) >= 0.01:
        results.append({
            'date': str(date)[:10],
            'operation': operation,
            'value': fx_diff_principal,
            'currency': 'RON',
            'bnr': 1,
            'value_ron': fx_diff_principal,
            'transactionid': f"{deposit_id} FXDEP {str(date)[:10]}",
        })
    
    # FX difference on accrued interest
    fx_diff_interest = (accrued_interest_cumulative * bnr_rate_end - 
                       accrued_interest_cumulative_ron - accrued_fx_cumulative_ron)
    accrued_fx_cumulative_ron += fx_diff_interest
    
    operation = 'FX_DIF_ACCRUAL_PLUS' if fx_diff_interest >= 0 else 'FX_DIF_ACCRUAL_MINUS'
    if abs(fx_diff_interest) >= 0.01:
        results.append({
            'date': str(date)[:10],
            'operation': operation,
            'value': fx_diff_interest,
            'currency': 'RON',
            'bnr': 1,
            'value_ron': fx_diff_interest,
            'transactionid': f"{deposit_id} FXINT {str(date)[:10]}",
        })
```

##### 4. Maturity Processing
```python
if end_date <= max_date:
    # Interest received
    if incasat > 0:
        operation = 'INC_DOBANDA_LEI' if currency == 'RON' else 'INC_DOBANDA_VALUTA'
        results.append({
            'date': str(end_date)[:10],
            'operation': operation,
            'value': incasat,
            'currency': currency,
            'bnr': bnr_rate,
            'value_ron': incasat * bnr_rate,
            'transactionid': f"{deposit_id} DOBANDA {str(end_date)[:10]}",
            'idx': 'ADAUGA' if ultima_lichidare else None,
        })
    
    # Accrual reversal
    if accrued_interest_cumulative > 0:
        operation = 'ACCRUAL_INTEREST_LEI' if currency == 'RON' else 'ACCRUAL_INTEREST_VALUTA'
        results.append({
            'date': str(end_date)[:10],
            'operation': operation,
            'value': -accrued_interest_cumulative,
            'currency': currency,
            'bnr': bnr_rate,
            'storno': True,
            'value_ron': -accrued_interest_cumulative * bnr_rate,
            'transactionid': f"{deposit_id} REVERSAL {str(end_date)[:10]}",
            'idx': 'ADAUGA' if ultima_lichidare else None,
        })
    
    # Principal repayment
    operation = 'MATURITATE_DEP_VALUTA' if currency != 'RON' else 'MATURITATE_DEP_LEI'
    results.append({
        'date': str(end_date)[:10],
        'operation': operation,
        'value': principal,
        'currency': currency,
        'bnr': bnr_rate,
        'value_ron': principal * bnr_rate,
        'quantity': -principal,
        'transactionid': f"{deposit_id} MAT {str(end_date)[:10]}",
        'idx': 'ADAUGA' if ultima_lichidare else None,
    })
```

#### Output Generation
```python
# Convert to DataFrame and format
output_df = pd.DataFrame(results)
output_df['custodian'] = custodian
output_df['ubo'] = "DD"
output_df['partner'] = output_df['custodian']
output_df['account'] = output_df['custodian'] + '_' + output_df['currency']
output_df['instrument'] = output_df['custodian'] + '_' + str(deposit_id)
output_df['details'] = str(deposit_id) + ' ' + output_df['operation']

# Rounding
output_df['value'] = output_df['value'].round(2)
output_df['value_ron'] = output_df['value_ron'].round(2)

# Query existing journal IDs
journal_map = pd.DataFrame(
    Journal.objects.filter(transactionid__in=output_df['transactionid'].tolist())
    .values('id', 'transactionid')
)
if len(journal_map) > 0:
    output_df = output_df.merge(journal_map, on='transactionid', how='left')
```

#### Usage
```bash
# Calculate deposit accruals
python manage.py deposit_accruals

# Output location
files/accruals/accruals_YYYY-MM-DD_HH-MM.xlsx
```

---

## Data Validation

### `fix_bnr_rates.py`
**File**: `port/management/commands/fix_bnr_rates.py`

#### Purpose
Validates BNR exchange rates in the journal against the official BNR database and identifies discrepancies for correction.

#### Validation Process

##### 1. Data Collection
```python
# Get journal entries with currency information
journal_qs = Journal.objects.select_related(
    'operation', 'instrument', 'instrument__currency'
).values(
    'id', 'transactionid', 'ubo__ubo_code', 'custodian__custodian_code',
    'instrument__symbol', 'instrument__currency__currency_code', 
    'operation__operation_code', 'date', 'value', 'bnr', 'value_ron'
)

# Get official BNR rates
bnr_qs = Bnr.objects.filter(
    currency_code__currency_code__in=['EUR', 'USD', 'MXN']
).values('currency_code__currency_code', 'date', 'value_exact')
```

##### 2. Rate Validation Logic
```python
# RON currency validation
error_ron = df[
    (df['currency'] == 'RON')
    & (df['bnr'] != 1)
].copy()

if len(error_ron) > 0:
    print('RON rate errors:', error_ron)
else:
    print('RON: No errors')
```

##### 3. BNR Rate Matching
```python
# Attach correct BNR rates using merge_asof
df = pd.merge_asof(
    left=df, right=df_bnr, on='date', by=['currency'],
    allow_exact_matches=False, direction='backward'
)
df.loc[df['currency']=='RON', 'bnrx'] = 1

# Attach end-of-month rates for comparison
df_bnr.rename(columns={'bnrx': 'bnr_eom'}, inplace=True)
df = pd.merge_asof(
    left=df, right=df_bnr, on='date', by=['currency'],
    allow_exact_matches=True, direction='backward'
)
```

##### 4. Error Detection
```python
# Find rate discrepancies
errors = df[
    ((df['bnr'] - df['bnrx']).abs() > 0.0001)
].copy()

print(f'{len(errors)} rate errors found')
```

##### 5. Correction Calculation
```python
if len(errors) > 0:
    # Calculate corrected values
    errors['bnr_incorrect'] = errors['bnr']
    errors['bnr'] = errors['bnrx']
    errors['value_ron'] = (errors['bnr'] * errors['value']).round(2)
    
    # Save error report
    save_path = settings.FILE_ROOT + "reports/bnr_errors.xlsx"
    with pd.ExcelWriter(save_path) as writer:
        errors.to_excel(writer, sheet_name='BNR Errors', index=False)
    
    print(f'Errors saved to {save_path}')
else:
    print('No BNR rate errors found')
```

#### Error Report Format
```python
# Error report columns
error_columns = [
    'id',                    # Journal entry ID
    'transactionid',         # Transaction identifier
    'date',                  # Transaction date
    'currency',              # Currency code
    'operation',             # Operation type
    'instrument',            # Instrument symbol
    'value',                 # Original currency value
    'bnr_incorrect',         # Incorrect rate used
    'bnr',                   # Correct BNR rate
    'value_ron',             # Corrected RON value
]
```

#### Usage
```bash
# Validate BNR rates
python manage.py fix_bnr_rates

# Output (if errors found)
files/reports/bnr_errors.xlsx
```

### `dif_fx.py`
**File**: `port/management/commands/dif_fx.py`

#### Purpose
Calculates cumulative FX differences from foreign exchange transactions for daily reconciliation and reporting.

#### Processing Logic

##### 1. Data Collection
```python
# Get FX-related journal entries
journal_qs = Journal.objects.filter(
    operation__operation_code__startswith='FX_'
).select_related('operation', 'instrument', 'instrument__currency')

# Exclude existing FX difference entries
df = df[~df['operation'].str.contains('DIF_')]
```

##### 2. Daily Aggregation
```python
# Aggregate by date and entity
INDEX_COLS = ['ubo', 'custodian', 'date']

dx = df.pivot_table(
    index=INDEX_COLS,
    values=['value_ron'],
    aggfunc={'value_ron': 'sum'}
).reset_index()

# Round and format
dx['value_ron'] = dx['value_ron'].round(2)
dx['value'] = dx['value_ron']
```

##### 3. Journal Entry Generation
```python
# Create FX difference entries
dx['instrument'] = dx['custodian'] + '_RON'
dx['account'] = dx['instrument']
dx['partner'] = dx['custodian']
dx['id'] = None
dx['quantity'] = 0
dx['transactionid'] = 'FX_DIF_' + dx['date'].astype(str).str[:10]
dx['details'] = 'Daily cumulative FX difference ' + dx['transactionid']

# Classify as plus or minus
dx['operation'] = 'FX_DIF_DIN_FX_PLUS'
dx.loc[dx['value_ron'] < 0, 'operation'] = 'FX_DIF_DIN_FX_MINUS'
```

#### Output Generation
```python
# Save comprehensive report
save_path = settings.FILE_ROOT + "reports/dif_curs_schimb.xlsx"
with pd.ExcelWriter(save_path) as writer:
    df.to_excel(writer, sheet_name='Existing', index=False)
    dx.to_excel(writer, sheet_name='DifCursSchimb', index=False)

# Export for journal import
dx.to_excel(
    settings.FILE_ROOT + "reports/dif_curs_schimb_export.xlsx",
    sheet_name='Export',
    index=False
)
```

#### Usage
```bash
# Calculate FX differences
python manage.py dif_fx

# Output files
files/reports/dif_curs_schimb.xlsx
files/reports/dif_curs_schimb_export.xlsx
```

---

## Utility Commands

### `test_flex_ibkr.py`
**File**: `brokers/management/commands/test_flex_ibkr.py`

#### Purpose
Testing command for IBKR Flex Query functionality without data processing.

#### Implementation
```python
from .lib.ibkr import IbkrApi

class Command(BaseCommand):
    def handle(self, *args, **options):
        # Test IBKR API connection and query execution
        IbkrApi.flex_query(self, settings.IBKR_QUERY)
        IbkrApi.xml_to_pandas(self)
```

#### Usage
```bash
# Test IBKR connection
python manage.py test_flex_ibkr
```

### `_upload_mst_port.py`
**File**: `brokers/management/commands/_upload_mst_port.py`

#### Purpose
Imports Morgan Stanley portfolio data from Excel files (legacy/inactive command).

#### Processing Steps

##### 1. File Import
```python
broker = 'mst'
file_root = settings.FILE_ROOT + f"{broker}/"

tabs = ['holdings', 'activity']
for t in tabs:
    pattern = f'*{t}*.xlsx'
    list_of_files = glob.glob(file_root + pattern)
    latest_file = max(list_of_files, key=os.path.getctime)
    df = pd.read_excel(latest_file)
    tab[t] = df
```

##### 2. Instrument Processing
```python
# Extract instrument information
secinfo = tab['holdings']
rename = {}  # Column mapping if needed
cols = ['symbol', 'name', 'cusip']
secinfo.columns = secinfo.columns.str.lower()
secinfo = secinfo.rename(columns=rename)[cols]
secinfo.loc[secinfo['symbol'].isna(), 'symbol'] = secinfo['cusip']
secinfo['currency'] = 'USD'
secinfo['isin'] = '??' + secinfo['symbol'] + '?'
```

##### 3. Portfolio Import
```python
portfolio = tab['holdings']
rename = {
    'market value ($)': 'value',
    'adjusted cost ($)': 'cost',
    'as of': 'date',
}
cols = ['symbol', 'cusip', 'value', 'cost', 'date', 'quantity']
portfolio = portfolio.rename(columns=rename)[cols]
portfolio.loc[portfolio['symbol'].isna(), 'symbol'] = portfolio['cusip']
portfolio['currency'] = 'USD'
portfolio['ubo'] = 'DD'
```

#### Status
This command is marked as inactive (`_upload_mst_port.py`) and serves as a template for Morgan Stanley integration.

---

## Command Dependencies

### Execution Order
```mermaid
graph TD
    A[curs_bnr] --> B[api_ibkr]
    A --> C[api_tdv]
    B --> D[upload_ibkr_port]
    C --> E[upload_tdv_port]
    D --> F[uptrz_ibkr]
    E --> G[uptrz_tdv]
    F --> H[bond_accruals_ql]
    G --> H
    H --> I[deposit_accruals]
    I --> J[fix_bnr_rates]
    J --> K[dif_fx]
    K --> L[uptrz_manual]
```

### Critical Dependencies

#### Daily Workflow
1. **BNR Rates**: Must run before any FX calculations
2. **Broker APIs**: Must complete before portfolio/transaction processing
3. **Portfolio Import**: Must precede transaction processing for reconciliation
4. **Journal Generation**: Requires complete instrument and account setup

#### Monthly Workflow
1. **Accrual Calculations**: Require updated BNR rates and journal entries
2. **Validation Commands**: Run after all data import and calculations
3. **Manual Import**: Final step after automated processing

### File Dependencies
```python
# IBKR workflow
files/ibkr/raw/statements_*.txt          # From api_ibkr
files/ibkr/daily/*.csv                   # From api_ibkr -> upload_ibkr_port
files/reports/journal_ibkr_*.xlsx        # From uptrz_ibkr

# TDV workflow  
files/tdv/*.csv                          # From api_tdv -> upload_tdv_port
files/reports/journal_tdv_*.xlsx         # From uptrz_tdv

# Calculations
files/accruals/bond_accruals_*.xlsx      # From bond_accruals_ql
files/accruals/accruals_*.xlsx           # From deposit_accruals

# Validation
files/reports/bnr_errors.xlsx            # From fix_bnr_rates
files/reports/dif_curs_schimb.xlsx       # From dif_fx
```

---

## Error Handling

### Common Error Patterns

#### Network Errors
```python
# Retry mechanism via cron scheduling
# Example: BNR import with two daily attempts
30 13 * * 1,2,3,4,5 python manage.py curs_bnr
30 15 * * 1,2,3,4,5 python manage.py curs_bnr
```

#### Database Errors
```python
# Bulk operations with conflict resolution
try:
    Journal.objects.bulk_create(
        model_instances,
        update_conflicts=True,
        unique_fields=unique,
        update_fields=update_fields,
    )
except IntegrityError as e:
    print(f"Database integrity error: {e}")
    # Handle missing foreign keys
```

#### Data Validation Errors
```python
# Missing foreign key validation
vals = df['operation'].unique()
all_vals = Operation.objects.values_list('operation_code', flat=True)
missing = set(vals) - set(all_vals)
if len(missing) > 0:
    print('Missing operations:', missing)
    # Create missing operations or halt processing
```

#### API Errors
```python
# IBKR API error handling
if dict_data['Status'] != 'Success':
    raise Exception(f"Error in Flex Query: {flex_req.text}")

# Timeout handling
if wait_time >= MAX_WAIT:
    raise TimeoutError("Timeout waiting for IBKR report")
```

### Error Recovery Strategies

#### File-Based Recovery
```python
# Save intermediate results for recovery
save_file = settings.FILE_ROOT + f"temp/intermediate_{today}.csv"
df.to_csv(save_file, index=False)
```

#### Database Rollback
```python
# Use database transactions for atomic operations
from django.db import transaction

try:
    with transaction.atomic():
        # Bulk database operations
        pass
except Exception as e:
    # Automatic rollback on error
    print(f"Transaction rolled back: {e}")
```

#### Validation Checkpoints
```python
# Reconciliation at each step
if len(errors) > 0:
    print(f"Validation failed: {len(errors)} errors")
    # Save error report and halt processing
    return False
```

---

## Performance Considerations

### Database Optimization

#### Bulk Operations
```python
# Use bulk_create instead of individual saves
Journal.objects.bulk_create(
    model_instances,
    batch_size=1000,  # Process in batches
    update_conflicts=True
)
```

#### Query Optimization
```python
# Use select_related for foreign keys
queryset = Journal.objects.select_related(
    'ubo', 'custodian', 'instrument', 'operation'
).values(...)

# Use values() to limit data transfer
.values('id', 'date', 'value', 'instrument__symbol')
```

#### Index Usage
```sql
-- Recommended indexes (SQLite compatible)
CREATE INDEX IF NOT EXISTS idx_journal_date ON port_journal(date);
CREATE INDEX IF NOT EXISTS idx_journal_custodian ON port_journal(custodian_id);
CREATE INDEX IF NOT EXISTS idx_bnr_currency_date ON port_bnr(currency_code_id, date);
```

### Memory Management

#### DataFrame Processing
```python
# Process large files in chunks
chunk_size = 10000
for chunk in pd.read_csv(file, chunksize=chunk_size):
    process_chunk(chunk)
    del chunk  # Explicit memory cleanup
```

#### File Cleanup
```python
# Clean up temporary files
import os
temp_files = glob.glob('/tmp/temp_*.csv')
for file in temp_files:
    os.remove(file)
```

### Execution Time Monitoring

#### Typical Execution Times
```python
# Command execution benchmarks
curs_bnr:           5-15 seconds
api_ibkr:           30-120 seconds (depends on data volume)
api_tdv:            15-45 seconds
upload_*_port:      10-30 seconds
uptrz_*:            60-300 seconds (depends on transaction volume)
bond_accruals_ql:   120-600 seconds (depends on bond count)
deposit_accruals:   30-120 seconds
```

#### Performance Monitoring
```python
import time
start_time = time.time()

# Command execution
process_data()

execution_time = time.time() - start_time
print(f"Execution time: {execution_time:.2f} seconds")
```

---

## Configuration Management

### Environment Variables
```bash
# Required for all commands
SECRET_KEY=your_secret_key
DB_PASS=your_db_password
DB_HOST=localhost

# IBKR specific
IBKR_TOKEN=your_ibkr_token
IBKR_QUERY=your_query_id
IBKR_QUERY_DAILY=daily_query_id

# TDV specific
USER_TDV=tradeville_username
PASS_TDV=tradeville_password
TDV_DD=person_id_1
TDV_AS=person_id_2
TDV_CP=person_id_3
```

### File System Structure
```
files/
├── ibkr/
│   ├── raw/           # Raw API responses
│   └── daily/         # Processed CSV files
├── tdv/               # TDV CSV exports
├── mst/               # Morgan Stanley files
├── accruals/          # Calculation results
├── reports/           # Final reports
└── temp/              # Temporary files
```

### Logging Configuration
```python
# Command output logging
LOGGING = {
    "version": 1,
    "handlers": {
        "file": {
            "level": "DEBUG",
            "class": "logging.FileHandler",
            "filename": os.path.join(BASE_DIR, '.logs/django.log'),
        },
    },
}
```

---

## Security Considerations

### Credential Management
- All API credentials stored in environment variables
- No hardcoded passwords or tokens in source code
- Database connections use limited-privilege accounts

### Data Protection
- Period locking prevents historical data modification
- Audit trail tracks all database changes
- File system permissions restrict access to sensitive data

### Network Security
- HTTPS/WSS connections for all broker APIs
- Certificate validation for external connections
- Rate limiting to prevent API abuse

---

## Troubleshooting Guide

### Common Issues and Solutions

#### 1. BNR Import Failures
```bash
# Check network connectivity
curl -I https://bnr.ro/nbrfxrates10days.xml

# Verify XML format
python -c "import requests; import xmltodict; print(xmltodict.parse(requests.get('https://bnr.ro/nbrfxrates10days.xml').content))"
```

#### 2. IBKR API Issues
```bash
# Test token validity
curl "https://ndcdyn.interactivebrokers.com/AccountManagement/FlexWebService/SendRequest?t=TOKEN&q=QUERY&v=3"

# Check query configuration in IBKR portal
# Verify query includes all required report types
```

#### 3. TDV Connection Problems
```python
# Test WebSocket connection
from websocket import create_connection
ws = create_connection("wss://portal.tradeville.ro/", subprotocols=["pf4"])
print("Connection successful")
```

#### 4. Database Lock Issues
```sql
-- Check for active locks
SELECT * FROM port_journal WHERE lock = true;

-- Reset locks if needed (caution!)
UPDATE port_journal SET lock = false WHERE date > 'YYYY-MM-DD';
```

#### 5. Missing Foreign Keys
```python
# Check for missing entities before import
missing_operations = set(df['operation'].unique()) - set(Operation.objects.values_list('operation_code', flat=True))
missing_instruments = set(df['symbol'].unique()) - set(Instrument.objects.values_list('symbol', flat=True))
```

#### 6. Memory Issues
```python
# Monitor memory usage
import psutil
import gc

def check_memory():
    process = psutil.Process()
    memory_info = process.memory_info()
    print(f"RSS: {memory_info.rss / 1024 / 1024:.1f} MB")
    print(f"VMS: {memory_info.vms / 1024 / 1024:.1f} MB")
    
# Force garbage collection
gc.collect()
```

#### 7. File Permission Issues
```bash
# Check file permissions
ls -la files/
ls -la files/ibkr/
ls -la files/tdv/

# Fix permissions if needed
sudo chown -R app:app files/
chmod -R 755 files/
```

#### 8. Cron Job Issues
```bash
# Check cron logs
grep CRON /var/log/syslog
tail -f /var/log/cron.log

# Test cron environment
# Create test script with same environment
env > /tmp/cron_env.txt
```

#### 9. QuantLib Installation Issues
```bash
# Install QuantLib dependencies
sudo apt-get install libquantlib0-dev
pip install --upgrade quantlib-python

# Test QuantLib import
python -c "import QuantLib as ql; print(ql.__version__)"
```

#### 10. Database Connection Issues
```python
# Test database connection
from django.db import connection
try:
    with connection.cursor() as cursor:
        cursor.execute("SELECT 1")
        print("Database connection successful")
except Exception as e:
    print(f"Database connection failed: {e}")
```

### Monitoring and Alerting

#### Health Check Script
```python
#!/usr/bin/env python
import os
import sys
import django
from datetime import datetime, timedelta

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'nch.settings')
django.setup()

from port.models import Bnr, Journal, Portfolio

def health_check():
    issues = []
    
    # Check recent BNR rates
    latest_bnr = Bnr.objects.filter(currency_code__currency_code='EUR').latest('date')
    if (datetime.now().date() - latest_bnr.date).days > 5:
        issues.append(f"BNR rates outdated: {latest_bnr.date}")
    
    # Check recent journal entries
    latest_journal = Journal.objects.latest('date')
    if (datetime.now().date() - latest_journal.date).days > 10:
        issues.append(f"Journal entries outdated: {latest_journal.date}")
    
    # Check recent portfolio data
    latest_portfolio = Portfolio.objects.latest('date')
    if (datetime.now().date() - latest_portfolio.date).days > 7:
        issues.append(f"Portfolio data outdated: {latest_portfolio.date}")
    
    if issues:
        print("HEALTH CHECK FAILED:")
        for issue in issues:
            print(f"  - {issue}")
        sys.exit(1)
    else:
        print("HEALTH CHECK PASSED")
        sys.exit(0)

if __name__ == "__main__":
    health_check()
```

#### Log Analysis
```bash
# Analyze command execution logs
tail -100 /home/<USER>/nch/logs/curs_bnr.log
grep ERROR /home/<USER>/nch/logs/*.log
grep "records" /home/<USER>/nch/logs/*.log | tail -10
```

#### Automated Monitoring
```bash
# Add to crontab for monitoring
0 9 * * 1,2,3,4,5 /home/<USER>/.django/bin/python /home/<USER>/nch/health_check.py || echo "NCH Health Check Failed" | mail -s "NCH Alert" <EMAIL>
```

---

**Document Version**: 2.0  
**Last Updated**: June 2025
**Maintainer**: Stelian Mitu