from django.core.management.base import BaseCommand
from django.conf import settings
from port.models import Journal, Bnr, Instrument
import pandas as pd
from datetime import datetime, date
from .lib.bondlib import EnhancedBondAccrualCalculator
import QuantLib as ql
from dateutil.relativedelta import relativedelta


class Command(BaseCommand):
    help = "Calculate bond accruals and related operations"

    def __init__(self):
        super().__init__()
        self.calculator = (
            EnhancedBondAccrualCalculator()
        )  # Create instance of calculator

    def handle(self, *args, **options):
        # 1. Query Journal for bond operations
        df_bonds, df_bond_info, df_bnr, df_bnr_eom = (
            self.calculator.query_data()
        )

        # print(df_bnr_eom[df_bnr_eom['date']>='2024-03-29'])

        # 4. Filter for specific bond
        # df_bond_info = df_bond_info[df_bond_info['symbol']=='ROMANI 5 1/2 09/18/28']
        # df_bond_info = df_bond_info[
        #     df_bond_info["symbol"] == "T 4 1/8 11/15/32"
        # ]

        df_bond_info = df_bond_info[
            df_bond_info["symbol"] == "T 4 1/8 06/15/26"
        ]
        # df_bond_info = df_bond_info[df_bond_info['symbol']=='US.A3K31L']
        # df_bond_info = df_bond_info[df_bond_info['symbol']=='T 4 10/31/29']
        # df_bond_info = df_bond_info[df_bond_info['symbol']=='POLAND 1 1/2 09/09/25']
        # df_bond_info = df_bond_info[df_bond_info['symbol']=='T 4 3/8 11/30/28']
        # df_bond_info = df_bond_info[df_bond_info['symbol']=='TURKEY 8 02/14/34'] # error
        # df_bond_info = df_bond_info[df_bond_info['symbol']=='US.A3LKN1']
        # df_bond_info = df_bond_info[df_bond_info['symbol']=='REPHUN 6 1/8 05/22/28']
        # df_bond_info = df_bond_info[df_bond_info['symbol']=='US.A3KXYT']
        # df_bond_info = df_bond_info[df_bond_info['symbol']=='T 4 3/8 11/30/28']
        # df_bond_info = df_bond_info[df_bond_info['symbol']=='ROMANI 3 02/27/27']
        # df_bond_info = df_bond_info[df_bond_info['symbol']=='DE.A3LENV']
        # df_bond_info = df_bond_info[df_bond_info['symbol']=='TBILL DUE:2024-12-19']

        df_all = pd.DataFrame()
        INDEX_COLS = [
            "ubo",
            "custodian",
            "partner",
            "date",
            "symbol",
            "currency",
            "operation",
            "account",
            "instrument",
        ]

        for symbol in df_bond_info["symbol"].unique():
            dx = df_bonds[df_bonds["symbol"] == symbol].copy()

            # Convert dx['date'] to datetime.date
            dx["date"] = pd.to_datetime(dx["date"]).dt.date

            if len(dx) > 0:
                print(f"\nFound {len(dx)} operations for {symbol}")

                # Retrieve instrument parameters
                bond_info = df_bond_info[
                    df_bond_info["symbol"] == symbol
                ].to_dict("records")[0]
                print("bond_info:\n", pd.DataFrame(bond_info, index=[0]))

                # Multiply quantity by face value
                dx["quantity"] = dx["quantity"] * bond_info["face_value"]

                # Concatenate by date and operation
                dx = dx.pivot_table(
                    index=INDEX_COLS,
                    columns=[],
                    values=["value", "quantity", "details"],
                    aggfunc={
                        "value": "sum",
                        "quantity": "sum",
                        "details": lambda x: ",".join(list(set(x))),
                    },
                ).reset_index()

                # Get all coupon dates for reference
                coupon_dates = list(
                    self.calculator.get_coupon_schedule(bond_info)
                )
                coupon_dates = [
                    date(d.year(), d.month(), d.dayOfMonth())
                    for d in coupon_dates
                ]
                print(
                    "coupon_dates",
                    [d.strftime("%Y-%m-%d") for d in coupon_dates],
                )

                # Correct BOND_COUPON_RECEIVED_BROKER date for correct coupon date
                dx_coupon = dx[
                    dx["operation"] == "BOND_COUPON_RECEIVED_BROKER"
                ].copy()
                dx_coupon_dates = pd.DataFrame(
                    {
                        "date": coupon_dates,
                        "real_date": coupon_dates,
                    }
                )
                dx_coupon["date"] = pd.to_datetime(dx_coupon["date"])
                dx_coupon_dates["date"] = pd.to_datetime(
                    dx_coupon_dates["date"]
                )
                dx_coupon_dates["real_date"] = pd.to_datetime(
                    dx_coupon_dates["real_date"]
                )

                dx_coupon = pd.merge_asof(
                    dx_coupon,
                    dx_coupon_dates,
                    on="date",
                    direction="nearest",  ### CHANGED from backward
                    tolerance=pd.Timedelta(days=5),
                    allow_exact_matches=True,
                )
                dx_coupon["date"] = dx_coupon["real_date"].dt.date

                print(dx_coupon)
                # print(dx_coupon_dates[dx_coupon_dates['date'] >= '2024-01-01'])

                dx_coupon = dx_coupon.drop(columns=["real_date"])

                # Replace BOND_COUPON_RECEIVED_BROKER with correct date
                dx = dx[dx["operation"] != "BOND_COUPON_RECEIVED_BROKER"]
                dx = pd.concat([dx, dx_coupon], axis=0)
                dx["date"] = pd.to_datetime(dx["date"]).dt.date

                # Set top priority for hard data except for BOND_COUPON_RECEIVED_BROKER
                dx["priority"] = 1
                dx.loc[
                    dx["operation"] == "BOND_COUPON_RECEIVED_BROKER", "priority"
                ] = 10

                print(dx)

                # Find calculations range
                start_date = dx["date"].min()
                end_date = min(datetime.today().date(), bond_info["maturity"])
                # accrual_days = self.calculator.get_last_trading_days(start_date, end_date, calendar_type=bond_info['calendar'])
                accrual_days = [
                    (
                        start_date.replace(day=1)
                        + relativedelta(months=i + 1)
                        - relativedelta(days=1)
                    )
                    for i in range(
                        (end_date.year - start_date.year) * 12
                        + end_date.month
                        - start_date.month
                        + 1
                    )
                ]
                fx_reev_days = [x for x in accrual_days]

                other_accrual_dates = dx["date"].unique()
                other_accrual_dates = sorted(
                    list(set(other_accrual_dates) - set(accrual_days))
                )  # Make sure datetime.date format, not mixed

                print("Date range", start_date, end_date)
                print(
                    "accrual_days: ", " ".join([str(x) for x in accrual_days])
                )
                print(
                    "other_accrual_dates: ",
                    " ".join([str(x) for x in other_accrual_dates]),
                )

                # Prepare blank dataframe with accrual dates
                all_accrual_dates = pd.DataFrame(
                    {
                        "operation": "INTEREST_ACCRUAL_BOND",
                        "date": accrual_days,
                        "details": f"Monthly accrual {symbol}",
                        "quantity": 0,
                        "priority": 4,
                    }
                ).drop_duplicates()

                other_dates_accrued = pd.DataFrame(
                    {
                        "operation": "INTEREST_ACCRUAL_BOND",
                        "date": other_accrual_dates,
                        "details": f"Interm. accrual trade date {symbol}",
                        "quantity": 0,
                        "priority": 3,
                    }
                ).drop_duplicates()

                all_accrual_dates = (
                    pd.concat([all_accrual_dates, other_dates_accrued])
                    .sort_values("date")
                    .reset_index(drop=True)
                )

                # Merge accrual dates into main dataframe
                dx = (
                    pd.concat([dx, all_accrual_dates], join="outer")
                    .sort_values(by=["date", "priority"])
                    .reset_index(drop=True)
                )

                # Fill missing info
                dx[INDEX_COLS] = dx[INDEX_COLS].ffill()

                # Cumulate quantities
                dx["quantity_total"] = dx["quantity"].fillna(0).cumsum()

                # Calculate accruals
                dx["accruals_total"] = dx.apply(
                    lambda row: self.calculator.calculate_accruals(
                        row["date"], row["quantity_total"], bond_info
                    ),
                    axis=1,
                )

                print("CALCULATED ACCRUALS")
                print(dx)

                # Cumulate settled interest by date and add to INTEREST_ACCRUAL_BOND
                op_updated = "INTEREST_ACCRUAL_BOND"
                ops_settled = [
                    "BOND_INTEREST_PAID_BROKER",
                    "BOND_INTEREST_RECEIVED_BROKER",
                ]
                dx["accrual_settled"] = 0.0
                dst = (
                    dx[dx["operation"].isin(ops_settled)]
                    .groupby("date")["value"]
                    .agg("sum")
                    .to_dict()
                )
                dx.loc[dx["operation"] == op_updated, "accrual_settled"] = dx[
                    dx["operation"] == op_updated
                ]["date"].map(dst)
                dx["accrual_settled"] = dx["accrual_settled"].fillna(0)

                # Set accrual to coupon at payment date - allow for coupon at end of month in weekend
                dx.sort_values(by=["date", "priority"], inplace=True)
                dx["date"] = pd.to_datetime(dx["date"])

                # dx_coupon = dx[dx['operation']=='BOND_COUPON_RECEIVED_BROKER'][['date', 'value']].rename(columns={'value': 'coupon_settled'})
                # dx_accruals = dx[dx['operation']=='INTEREST_ACCRUAL_BOND'].copy()
                # dx_rest = dx[~dx['operation'].isin(['BOND_COUPON_RECEIVED_BROKER', 'INTEREST_ACCRUAL_BOND'])].copy()
                # dx_accruals = pd.merge_asof(
                #     dx_accruals, dx_coupon,
                #     left_on='date', right_on='date',
                #     direction='backward',
                #     allow_exact_matches=False,
                #     tolerance=pd.Timedelta(days=5))

                # print(dx_accruals)

                coupon_payments_dict = (
                    dx[dx["operation"] == "BOND_COUPON_RECEIVED_BROKER"][
                        ["date", "value"]
                    ]
                    .set_index("date")["value"]
                    .to_dict()
                )
                dx["coupon_settled"] = 0.0
                dx.loc[
                    dx["operation"] == "INTEREST_ACCRUAL_BOND", "coupon_settled"
                ] = dx["date"].map(coupon_payments_dict)
                dx["coupon_settled"] = dx["coupon_settled"].fillna(0)
                dx.loc[dx["coupon_settled"] > 0, "accruals_total"] = dx[
                    "coupon_settled"
                ]

                # Calculate incremental accruals excluding settled
                dac = dx[
                    dx["operation"].isin(
                        ["INTEREST_ACCRUAL_BOND", "BOND_COUPON_RECEIVED_BROKER"]
                    )
                ].copy()
                dac["accrual_total_shift"] = (
                    dac["accruals_total"].shift(1).fillna(0)
                )
                dac["accrual_incremental"] = (
                    dac["accruals_total"]
                    - dac["accrual_total_shift"]
                    + dac["accrual_settled"]
                )
                # print( dac  )
                dac = dac[dac["operation"] == "INTEREST_ACCRUAL_BOND"][
                    [
                        "date",
                        "operation",
                        "accrual_incremental",
                    ]
                ]

                # Port accruals to main dataframe
                dx = pd.merge(dx, dac, how="outer", on=["date", "operation"])
                dx.loc[dx["operation"] == "INTEREST_ACCRUAL_BOND", "value"] = (
                    dx["accrual_incremental"]
                )

                # print(dx)

                # Add BNR
                dx["date"] = pd.to_datetime(dx["date"])
                dx = pd.merge_asof(
                    left=dx,
                    right=df_bnr,
                    on="date",
                    by=["currency"],
                    allow_exact_matches=False,
                    direction="backward",
                )

                # Add BNR end of month
                dx = pd.merge_asof(
                    left=dx,
                    right=df_bnr_eom,
                    on="date",
                    by=["currency"],
                    allow_exact_matches=True,
                    direction="backward",
                )

                print(dx)

                # RON value
                dx["value_ron"] = dx["value"] * dx["bnr"]
                dx.sort_values(by=["date", "priority"], inplace=True)

                # FX diff calc - select records
                dfx = dx[
                    dx["operation"].isin(
                        ["INTEREST_ACCRUAL_BOND", "BOND_COUPON_RECEIVED_BROKER"]
                    )
                ].copy()
                # dfx = dfx[['date', 'operation', 'details', 'value', 'accruals_total', 'bnr', 'bnr_eom',  ]]
                dfx["revalue"] = False
                dfx.loc[dfx["date"].isin(fx_reev_days), "revalue"] = True

                # # Remove non-end of month records
                # dfx = dfx[
                #     (dfx['operation']!='INTEREST_ACCRUAL_BOND') | (dfx['revalue']==True)
                #     ]

                # Calculate full accruals
                dfx["accrual"] = dfx["accruals_total"] - dfx[
                    "accruals_total"
                ].shift(1).fillna(0)

                # Reverse all accruals at coupon payment
                dfx.loc[
                    dfx["operation"] == "BOND_COUPON_RECEIVED_BROKER", "accrual"
                ] = 0
                dfx["accrual_valuta"] = dfx["accrual"]
                dfx.loc[
                    dfx["operation"] == "BOND_COUPON_RECEIVED_BROKER",
                    "accrual_valuta",
                ] = -dfx["accrual"].cumsum()
                dfx["accrual_ron"] = (dfx["accrual_valuta"] * dfx["bnr"]).round(
                    2
                )

                # Cumulate amounts
                dfx["total_accrual_valuta"] = dfx["accrual_valuta"].cumsum()
                dfx["total_accrual_ron"] = dfx["accrual_ron"].cumsum()

                # Revalue total amounts
                # dfx['revalue_total'] = 0.0
                # dfx.loc[dfx['revalue'], 'revalue_total'] = (dfx['total_accrual_valuta'] * dfx['bnr_eom']).round(2)
                dfx["revalue_total"] = (
                    dfx["total_accrual_valuta"] * dfx["bnr"]
                ).round(2)
                dfx.loc[dfx["revalue"], "revalue_total"] = (
                    dfx["total_accrual_valuta"] * dfx["bnr_eom"]
                ).round(2)

                # Calculate total FX
                dfx["total_fx_diff"] = (
                    dfx["revalue_total"] - dfx["total_accrual_ron"]
                )

                # Calculate incremental FX difference
                dfx["fx_diff"] = dfx["total_fx_diff"] - dfx[
                    "total_fx_diff"
                ].shift(1).fillna(0)

                # Rounding
                round_cols = [
                    "value",
                    "accruals_total",
                    "accrual",
                    "accrual_valuta",
                    "accrual_ron",
                    "total_accrual_valuta",
                    "total_accrual_ron",
                    "revalue_total",
                    "total_fx_diff",
                    "fx_diff",
                ]
                dfx[round_cols] = dfx[round_cols].round(2)

                # Save
                save_path = (
                    settings.FILE_ROOT + f"accruals/exemplu_diferente_curs.xlsx"
                )
                dfx.to_excel(save_path, index=False)

                #         # Drop intermediary BOND_COUPON_RECEIVED_BROKER and dates inside month
                #         dfx = dfx[dfx['operation']!='BOND_COUPON_RECEIVED_BROKER']

                #         fx_days = [str(x)[:10] for x in fx_reev_days]
                #         dfx = dfx[dfx['date'].isin(fx_days)]

                dfx["details"] = "FxDifAcc " + dfx["details"]

                dfx["operation"] = "FX_DIF_ACCRUAL_PLUS"
                dfx.loc[dfx["fx_diff"] < 0, "operation"] = (
                    "FX_DIF_ACCRUAL_MINUS"
                )
                dfx["value"] = dfx["fx_diff"]

                # Set instrument for FX_DIF_ACCRUAL to RON
                dfx["instrument"] = dfx["custodian"] + "_" + dfx["symbol"]
                dfx["account"] = dfx["custodian"] + "_RON"
                dfx["value_ron"] = dfx["value"]
                dfx["currency"] = "RON"
                dfx["bnr"] = 1

                drop_print = [
                    "symbol",
                    "details",
                    "account",
                    "ubo",
                    "custodian",
                    "partner",
                    "currency",
                    "priority",
                    "quantity",
                ]
                print("\ndfx\n", dfx.drop(columns=drop_print))

                # Merge dfx into dx and sort
                dx = pd.concat([dx, dfx], ignore_index=True)
                dx["date"] = pd.to_datetime(dx["date"])
                dx.sort_values(
                    by=["date", "priority"], inplace=True, ignore_index=True
                )

                df_all = pd.concat([df_all, dx], ignore_index=True)

            else:
                print(f"No operations found for {symbol}")

        # Save results
        today = str(datetime.today())[:10]
        save_path = settings.FILE_ROOT + f"accruals/bond_accruals_{today}.xlsx"

        # Format export
        # df_all['date'] = df_all['date'].astype(str)[:10]

        # FIlter by date
        # df_all = df_all[df_all['date']<='2024-10-31']

        round_cols = [
            "value",
            "accruals_total",
            "value_ron",
            "accrual_incremental",
        ]
        df_all[round_cols] = df_all[round_cols].round(2)
        df_all["id"] = None

        export_cols = [
            "ubo",
            "custodian",
            "partner",
            "account",
            "operation",
            "instrument",
            "symbol",
            "date",
            "value",
            "bnr",
            "value_ron",
            "quantity",
        ]
        df_all_export = df_all[export_cols].copy()

        export_cols = {
            "INTEREST_ACCRUAL_BOND": "ACC",
            "FX_DIF_ACCRUAL_MINUS": "FXDIF",
            "FX_DIF_ACCRUAL_PLUS": "FXDIF",
        }
        df_all_export = df_all_export[
            df_all_export["operation"].isin(export_cols.keys())
        ]
        df_all_export["op_symbol"] = df_all_export["operation"].map(export_cols)

        # Remove zeros
        df_all_export = df_all_export[df_all_export["value"].abs() != 0.0]

        df_all_export["details"] = (
            df_all_export["operation"] + " " + df_all_export["symbol"]
        )

        # Query existing journal IDs
        df_all_export["transactionid"] = (
            df_all_export["symbol"]
            + " "
            + df_all_export["op_symbol"]
            + " "
            + df_all_export["date"].astype(str)
        )

        journal_map = pd.DataFrame(
            Journal.objects.all().values("id", "transactionid")
        )

        if len(journal_map) > 0:
            # Merge journal IDs into output_df
            df_all_export = df_all_export.merge(
                journal_map, on="transactionid", how="left"
            )
        else:
            # If no matching journal records found, add empty id column
            df_all_export["id"] = None

        df_all_export["id"] = (
            df_all_export["id"].fillna(0.0).astype(int).replace(0, None)
        )

        # Filter by date
        #################### HARDCODED - TO BE REMOVED
        # df_all_export = df_all_export[df_all_export['date']>'2024-12-31']

        save_path_csv = (
            settings.FILE_ROOT + f"accruals/bond_accruals_export_{today}.csv"
        )
        df_all_export.to_csv(save_path_csv, index=False)

        # Deduplicate transactionid
        dupli = df_all_export[
            df_all_export.duplicated(subset=["transactionid"], keep=False)
        ].sort_values(by=["instrument"])
        if len(dupli) > 0:
            print("DUPLICATED:")
            print(dupli)

        with pd.ExcelWriter(save_path) as writer:
            df_bonds.to_excel(writer, sheet_name="All_Bonds", index=False)
            df_bnr.to_excel(writer, sheet_name="BNR_Rates", index=False)
            df_bnr_eom.to_excel(writer, sheet_name="BNR_ENd_Month", index=False)
            df_bond_info.to_excel(
                writer, sheet_name="Bond_Definitions", index=False
            )
            df_all.to_excel(writer, sheet_name="CALCULE", index=False)
            dupli.to_excel(writer, sheet_name="!!!DuplicateID", index=False)
            df_all_export.to_excel(writer, sheet_name="EXPORT", index=False)

        print(df_all_export)

        print(f"\nResults saved to {save_path}")
