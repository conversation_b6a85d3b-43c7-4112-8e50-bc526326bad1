from django.core.management.base import BaseCommand
from django.conf import settings
from port.models import Journal, Bnr, Instrument
import pandas as pd
from datetime import datetime, date
from dateutil.relativedelta import relativedelta

class Command(BaseCommand):
    help = 'Calculate bond accruals and related operations'

    def __init__(self):
        super().__init__()
        
    def handle(self, *args, **options):

        # Get related data
        journal_qs = Journal.objects.filter(
            operation__operation_code__startswith='FX_'
            ).select_related(
                'operation',
                'instrument',
                'instrument__currency',
            ).values(
                'id',
                'ubo__ubo_code',
                'custodian__custodian_code',
                'partner__partner_code',
                'operation__operation_code',
                'instrument__symbol',
                'instrument__currency__currency_code',
                'account__account_code',
                'date',
                'value',
                'quantity',
                'bnr',
                'value_ron',
                'details',
            )

        df = pd.DataFrame(list(journal_qs)).rename(columns={
                'instrument__currency__currency_code': 'currency',
                'operation__operation_code': 'operation',
                'instrument__symbol': 'symbol',
                'instrument__convention': 'convention',
                'instrument__calendar': 'calendar',
                'partner__partner_code': 'partner',
                'custodian__custodian_code': 'custodian',
                'account__account_code': 'account',
                'ubo__ubo_code': 'ubo',
            }).sort_values(['date', 'operation'])

        # df = df[df['custodian']=='UBS']

        print(df)

        df = df[~df['operation'].str.contains('DIF_')]


        print(df['operation'].unique())


        # Concatenate by date and operation
        INDEX_COLS = ['ubo', 'custodian',  'date',  ]

        dx = df.pivot_table(
            index=INDEX_COLS, 
            columns=[ ], 
            values=[
                'value_ron', 
                # 'quantity', 'details'
                ],
            aggfunc={
                'value_ron': 'sum',
                # 'quantity': 'sum',
                # 'details': lambda x: ','.join(list(set(x)))
            }).reset_index()


        dx['instrument'] = dx['custodian'] + '_RON'
        dx['account'] = dx['instrument'] 
        dx['partner'] = dx['custodian'] 
        dx['value_ron'] = dx['value_ron'].round(2)
        dx['value'] = dx['value_ron'] 
        dx['id'] = None
        dx['quantity'] = 0
        dx['transactionid'] =  'FX_DIF_'  + dx['date'].astype(str).str[:10]
        dx['details'] = 'Calcul cumulativ zilnic ' + dx['transactionid'] 

        dx['operation'] = 'FX_DIF_DIN_FX_PLUS'
        dx.loc[dx['value_ron']<0, 'operation'] = 'FX_DIF_DIN_FX_MINUS'

        save_path = settings.FILE_ROOT + f"reports/dif_curs_schimb.xlsx"
        with pd.ExcelWriter(save_path) as writer:
            df.to_excel(writer, sheet_name='Existing', index=False)
            dx.to_excel(writer, sheet_name='DifCursSchimb', index=False)

        dx.to_excel(settings.FILE_ROOT + f"reports/dif_curs_schimb_export.xlsx", sheet_name='Export', index=False)


        print(dx)