""" Functii IBKR 
Reference: https://www.ibkrguides.com/clientportal/performanceandstatements/flex3.htm
"""
import os
import glob
import json
import requests
import xmltodict
import pandas as pd
from datetime import datetime
from django.core.management.base import BaseCommand
from django.conf import settings


class IbkrApi:
    """IBKR API functions"""

    BROKER = 'ibkr'
    BASE_URL = "https://ndcdyn.interactivebrokers.com/AccountManagement/FlexWebService"
    TODAY = str(datetime.today())[:10]
    MAX_WAIT = 600  # timeout for downloading reports in seconds
    STATEMENTS_PATH = os.path.join(settings.FILE_ROOT, BROKER, "raw")
    SAVE_FILE = os.path.join(STATEMENTS_PATH, "statements_{}.txt".format(TODAY))

    def __init__(self):
        self.name = "IBKR API"
        self.today = datetime.today().strftime("%Y-%m-%d")
        self.save_file = os.path.join(self.STATEMENTS_PATH, f"statements_{self.today}.txt")

    @staticmethod
    def read_json_file(file_path):
        with open(file_path, 'r') as file:
            return json.load(file)

    @staticmethod
    def format_table(df):
        """Format table imported from IBKR"""
        # Remove '@' from column names
        df.columns = df.columns.str.lstrip('@')

        # Convert columns to lowercase
        df.columns = df.columns.str.lower()

        # Adjust dates
        date_columns = ['fromdate', 'reportdate', 'todate', 'datetime', 'tradedate', 
                        'settledatetarget', 'ordertime', 'settledate', 'whengenerated']
        for col in date_columns:
            if col in df.columns:
                df[col] = pd.to_datetime(df[col], format='mixed')
        
        return df

    def get_latest_statement_file(self, folder_path, pattern="statements_*.txt"):
        """Get the latest statement file from the specified folder"""
        pattern = os.path.join(folder_path, pattern)
        files = glob.glob(pattern)
        
        if not files:
            raise ValueError(f"No files matching 'statements_*.txt' found in {folder_path}")
        
        return max(files, key=os.path.getmtime)

    def flex_query(self, query_id: int):
        """Execute a Flex Statement Query"""
        # Get link to report
        url = f"{IbkrApi.BASE_URL}/SendRequest?t={settings.IBKR_TOKEN}&q={query_id}&v=3"
        flex_req = requests.get(url=url)

        # Parse the response
        dict_data = xmltodict.parse(flex_req.text)['FlexStatementResponse']
        if dict_data['Status'] != 'Success':
            raise Exception(f"Error in Flex Query: {flex_req.text}")

        request_url = f"{dict_data['Url']}?t={settings.IBKR_TOKEN}&q={dict_data['ReferenceCode']}&v=3"

        # Wait for report to be ready
        wait_time = 0
        while wait_time < IbkrApi.MAX_WAIT:
            report = requests.get(url=request_url)
            res = xmltodict.parse(report.text)

            if 'FlexQueryResponse' in res:
                break
            
            wait_time += 1
            print('.', end='', flush=True)

        if wait_time >= IbkrApi.MAX_WAIT:
            raise TimeoutError("Timeout waiting for IBKR report")

        print('\nReports downloaded')
        reports = res['FlexQueryResponse']['FlexStatements']['FlexStatement']
        
        # Save report to file
        with open(IbkrApi.SAVE_FILE, 'w') as file:
            json.dump(reports, file)
        print(f"{len(reports)} statements saved in {IbkrApi.SAVE_FILE}")

        return reports

    def xml_to_pandas(self, save_file=None):
        """Convert XML to pandas DataFrames"""
        with open(IbkrApi.SAVE_FILE, 'r') as file:
            data = file.read()

        # Convert XML to dictionary
        res = json.loads(data)

        # Retrieve IDs in a separate disctionary and remove them from main data
        ids = {k[1:]: v for k, v in res.items() if k.startswith('@')}
        res = {k: v for k, v in res.items() if not k.startswith('@')}

        # Formatare tabele importate din IBKR
        tables = {}
        for id_table, table in res.items():
            
            if table and isinstance(table, dict):
                if len(table)>1:
                    # Convert dictionary to dataframe
                    df = pd.DataFrame.from_dict(table, orient='index').T
                elif len(table)==1:
                    # Recurse to next dictionary
                    tx = list(table.values())[0]
                    if tx and isinstance(tx, list):
                        # Convert list of dictionaries to dataframe
                        df = pd.DataFrame(tx)
                    else:
                        df = pd.DataFrame.from_dict(tx, orient='index').T
                else:
                    print('No data or invalid format for', id_table)

                df = IbkrApi.format_table(df)
                tables[id_table] = df
                print(f'Table: {id_table}, records {len(df)}')

                # Save dataframe to file
                save_file = settings.FILE_ROOT + f"{IbkrApi.BROKER}/daily/{id_table}_{IbkrApi.TODAY}.csv"
                df.to_csv(save_file, index=False)
                del df

            else:
                print('No data or invalid format for', id_table)
                # print(table)

        return tables
