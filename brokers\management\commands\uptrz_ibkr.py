"""Export IBKR transactions"""

from django.core.management.base import BaseCommand
from django.conf import settings
from port.models import (
    Instrument,
    Ubo,
    Partner,
    Operation,
    Journal,
    Custodian,
    Account,
    Lock,
)
from django.db.models import Max  # Generates a "SELECT MAX..." query
from port.models import Lock
import glob
import os
import pandas as pd
import datetime


class Command(BaseCommand):

    def handle(self, *args, **options):

        broker = "ibkr"

        custodian_code = "IBKR"

        UPLOAD = True

        lock_date = Lock.objects.aggregate(Max("lock_date"))["lock_date__max"]
        print("lock_date", lock_date)

        UBO = "DD"

        # Get latest lock date
        lock_date = Lock.objects.aggregate(Max("lock_date"))["lock_date__max"]
        print("lock_date", lock_date)

        # Import tabele
        tabs = [
            "OpenPositions",
            "CashReport",
            "CashTransactions",
            "CorporateActions",  # Dividende etc
            "Trades",
            "SalesTaxes",  # TVA etc
            "TransactionTaxes",  # French tax on transactions
        ]

        tab = {}
        root = settings.FILE_ROOT + "{broker}/daily/".format(broker=broker)
        for t in tabs:
            list_of_files = glob.glob(root + t + "*")
            latest_file = max(list_of_files, key=os.path.getctime)
            df = pd.read_csv(latest_file).dropna(axis=1, how="all")
            tab[t] = df

        # OpenPositions
        cols = [
            "symbol",
            "isin",
            "position",
            "markprice",
            "positionvalue",
            "costbasisprice",
            "costbasismoney",
            "side",
            "accruedint",
            "reportdate",
        ]
        positions = tab["OpenPositions"][cols].round(2)
        positions = positions[
            positions["reportdate"] == max(positions["reportdate"])
        ]

        # CashReport
        cash = tab["CashReport"]

        col_float = [
            "startingcash",
            "commissions",
            "depositwithdrawals",
            "deposits",
            "withdrawals",
            "brokerinterest",
            "bondinterest",
            "nettradessales",
            "nettradespurchases",
            "withholdingtax",
            "fxtranslationgainloss",
            "endingcash",
            "slbnetcash",
            "salestax",
            "otherfees",
        ]
        cash[col_float] = cash[col_float].astype("float").round(2)
        cash = cash[
            [
                "accountid",
                "currency",
                "fromdate",
            ]
            + col_float
        ].copy()
        cash = cash[cash["fromdate"] == max(cash["fromdate"])]

        # CashTransactions
        cols = [
            "transactionid",
            "type",
            "currency",
            "assetcategory",
            "symbol",
            "isin",
            "datetime",
            "amount",
            "description",
        ]
        tr_cash = tab["CashTransactions"][cols].copy()
        tr_cash.loc[tr_cash["isin"].str.len() < 12, "isin"] = tr_cash["symbol"]
        tr_cash.rename(
            columns={
                "datetime": "date",
                "amount": "value",
                "description": "details",
                "type": "operation",
            },
            inplace=True,
        )

        # Corporate actions
        cols = [
            "transactionid",
            "type",
            "currency",
            "assetcategory",
            "symbol",
            "isin",
            "reportdate",
            "amount",
            "proceeds",
            "quantity",
            "description",
        ]
        corpact = tab["CorporateActions"][cols].copy()
        corpact.rename(
            columns={
                "reportdate": "date",
                "proceeds": "value",
                "description": "details",
                "type": "operation",
            },
            inplace=True,
        )

        # Trades
        cols = [
            "transactionid",
            "transactiontype",
            "currency",
            "assetcategory",
            "symbol",
            "isin",
            "tradedate",
            "quantity",
            "tradeprice",
            "proceeds",
            "ibcommission",
            "taxes",
            "netcash",
            "cost",
            "buysell",
            "accruedint",
            "fifopnlrealized",
        ]
        trades = tab["Trades"][cols].copy()
        trades.rename(
            columns={
                "accruedint": "accruedint0",
                "transactiontype": "operation",
                "tradedate": "date",
                "fifopnlrealized": "profit",
            },
            inplace=True,
        )

        final_columns = [
            "transactionid",
            "operation",
            "currency",
            "symbol",
            "date",
            "quantity",
            "value",
            "profit",
            "isin",
            "details",
        ]

        # SalesTaxes
        cols = [
            "transactionid",
            "currency",
            "date",
            "salestax",
            "taxtype",
            "taxabledescription",
        ]
        tax = tab["SalesTaxes"][cols].copy()
        tax.rename(
            columns={
                "salestax": "value",
                "taxtype": "operation",
                "taxabledescription": "details",
            },
            inplace=True,
        )

        # Stamp Duties
        cols = [
            "tradeid",
            "currency",
            "date",
            "taxamount",
            "taxdescription",
        ]
        stamp_tax = tab["TransactionTaxes"][cols].copy()
        stamp_tax.rename(
            columns={
                "tradeid": "transactionid",
                "taxamount": "value",
                "taxdescription": "details",
            },
            inplace=True,
        )
        stamp_tax["operation"] = "STAMP_DUTY_BROKER"
        stamp_tax["profit"] = 0
        stamp_tax["quantity"] = 0

        # Add commissions
        trades_comm = trades[trades["ibcommission"] != 0].copy()
        trades_comm["operation"] = "COMIS_BROKER_VALUTA"
        trades_comm["value"] = trades_comm["ibcommission"]
        trades_comm["profit"] = 0
        trades_comm["quantity"] = 0
        trades_comm["details"] = "commission"

        # # Add taxes
        # trades_taxes = trades[trades['taxes']!=0].copy()
        # trades_taxes['operation'] = 'STAMP_DUTY_BROKER'
        # trades_taxes['value'] = trades_taxes['taxes']
        # trades_taxes['profit'] = 0
        # trades_taxes['quantity'] = 0
        # trades_taxes['details'] = 'taxes'

        # Correct FX symbol
        trades_comm.loc[
            (trades_comm["symbol"].str.len() == 7)
            & ((trades_comm["symbol"].str[3] == ".")),
            "currency",
        ] = "USD"
        trades_comm.loc[
            (trades_comm["symbol"].str.len() == 7)
            & ((trades_comm["symbol"].str[3] == ".")),
            "symbol",
        ] = "USD"
        trades_comm = trades_comm[final_columns]

        # Add FX IN
        trades_fxin = trades[trades["assetcategory"] == "CASH"].copy()
        trades_fxin["operation"] = "FX_IN_BROKER"
        trades_fxin["symbol"] = trades_fxin["currency"]
        trades_fxin["value"] = trades_fxin["proceeds"]
        trades_fxin["quantity"] = trades_fxin["value"]
        trades_fxin["details"] = "fx in"
        trades_fxin["profit"] = 0
        trades_fxin = trades_fxin[final_columns]

        # Add FX out
        trades_fxout = trades[trades["assetcategory"] == "CASH"].copy()
        trades_fxout["operation"] = "FX_OUT_BROKER"
        trades_fxout["symbol"] = trades_fxout["symbol"].str[:3]
        trades_fxout["currency"] = trades_fxout["symbol"]
        trades_fxout["value"] = trades_fxout["quantity"]
        trades_fxout["details"] = "fx out"
        trades_fxout["profit"] = 0
        trades_fxout = trades_fxout[final_columns]

        # Reformat original table, no FX
        trades_orig = trades[trades["assetcategory"] != "CASH"].copy()
        trades_orig["operation"] = trades_orig["buysell"]
        trades_orig["value"] = trades_orig["proceeds"]
        trades_orig["details"] = "exchange trade"
        trades_orig = trades_orig[final_columns]

        # print('trades_profit\n', trades_profit, '\ntrades_accrued\n', trades_accrued, '\ntrades_com\n', trades_comm,
        #       '\ntrades_fxin\n', trades_fxin, '\ntrades_fxout\n', trades_fxout,
        #       '\ntrades_orig\n', trades_orig)

        # Trades - Separate and explode FX
        fx = trades[trades["assetcategory"] == "CASH"].copy()

        fx_out = fx.copy()
        fx_out["operation"] = "FX_OUT_BROKER"
        fx_out["symbol"] = fx_out["symbol"].str[:3]
        fx_out["currency"] = fx_out["symbol"]
        fx_out["value"] = fx_out["quantity"]

        fx_in = fx.copy()
        fx_in["operation"] = "FX_IN_BROKER"
        fx_in["symbol"] = fx_in["currency"]
        fx_in["value"] = fx_in["proceeds"]

        fx_com = fx.copy()
        fx_com["operation"] = "COMIS_BROKER_VALUTA"
        fx_com["symbol"] = fx_com["currency"]
        # fx_com['symbol'] = 'USD'
        fx_com["value"] = fx_com["ibcommission"]

        # Split commisions from trades
        trades = trades[trades["assetcategory"] != "CASH"].copy()
        trades["operation"] = trades["buysell"]
        trades["value"] = trades["proceeds"]

        trades_com = trades[trades["ibcommission"] != 0].copy()
        trades_com["operation"] = "COMIS_BROKER_VALUTA"
        trades_com["value"] = trades_com["ibcommission"]
        trades_com["quantity"] = 0

        trades = pd.concat(
            [fx_out, fx_in, fx_com, trades, trades_com, stamp_tax]
        ).sort_values(by="transactionid", ignore_index=True)
        trades.rename(columns={"buysell": "details"}, inplace=True)

        # Concatenate transactions
        # all = pd.concat([trades, tr_cash, corpact, tax], ignore_index=True)
        all = pd.concat(
            [
                trades_comm,
                stamp_tax,
                trades_fxin,
                trades_fxout,
                trades_orig,
                #  trades_accrued,
                tr_cash,
                corpact,
                tax,
            ],
            ignore_index=True,
        )

        # all['cost'] = all['proceeds'] + all['ibcommission']
        all[["value", "quantity"]] = all[["value", "quantity"]].fillna(0)
        all["date"] = all["date"].str[:10]
        all.loc[all["symbol"].isna(), "symbol"] = all["currency"]

        all = all[final_columns]
        all["ubo"] = "DD"
        all["profit"] = all["profit"].fillna(0.0)

        # Reclassify type
        all["operation"] = all["operation"].str.upper()
        reclas = {
            "DEPOSITS/WITHDRAWALS": "VIRAMENT",
            "BOND INTEREST PAID": "BOND_INTEREST_PAID_BROKER",
            "BOND INTEREST RECEIVED": "BOND_INTEREST_RECEIVED_BROKER",
            "WITHHOLDING TAX": "WHT_BROKER",
            "BROKER INTEREST PAID": "BROKER_INTEREST_PAID_BROKER",
            "BROKER INTEREST RECEIVED": "BROKER_INTEREST_RECEIVED",
            "OTHER FEES": "COMIS_BROKER_VALUTA",
            "TM": "SELL_BOND",
            "VAT": "COMIS_BROKER_VALUTA",
            "BUY": "BUY_BOND_BROKER",
            "SELL": "SELL_BOND_BANCA",
            "DIVIDENDS": "INC_DIVIDEND_BROKER",
            "COMMISSION ADJUSTMENTS": "COMIS_BROKER_VALUTA",
            "STAMP_DUTY_BROKER": "STAMP_DUTY_BROKER",
        }
        all.loc[all["operation"].isin(reclas.keys()), "operation"] = all[
            all["operation"].isin(reclas.keys())
        ]["operation"].map(reclas)

        # Pregatire Calcul cost mediu ponderat
        all["value_gross"] = all["value"]
        TRADES = ["BUY_BOND", "SELL_BOND"]
        all = all.sort_values(by=["date", "transactionid"], ignore_index=True)
        # Non-trades
        all_x = all[~(all["operation"].isin(TRADES))].copy()
        trades = all[all["operation"].isin(TRADES)].copy()

        # Calcul cost mediu ponderat
        if len(trades) > 0:
            df = pd.DataFrame()
            for symbol in trades["symbol"].unique():
                dx = (
                    trades[trades["symbol"] == symbol]
                    .reset_index(drop=True)
                    .copy()
                )
                dx[["value_at_cost", "unit_cost"]] = [0.0, 0.0]
                dx[["quantity", "value"]] = dx[["quantity", "value"]].astype(
                    float
                )

                latest_cost = 0
                cumulated_value = 0
                cumulated_quantity = 0

                for index, row in dx.iterrows():
                    if row["quantity"] >= 0:
                        dx.loc[index, "value_at_cost"] = -row["value"]
                        cumulated_value += -row["value"]
                        cumulated_quantity += row["quantity"]
                        latest_cost = cumulated_value / cumulated_quantity
                        dx.loc[index, "unit_cost"] = latest_cost
                    else:
                        cost = row["quantity"] * latest_cost
                        dx.loc[index, "value_at_cost"] = cost
                        cumulated_quantity += row["quantity"]
                        cumulated_value += cost

                        if cumulated_quantity > 0:
                            latest_cost = cumulated_value / cumulated_quantity
                        else:
                            latest_cost = 0
                        dx.loc[index, "unit_cost"] = latest_cost

                dx["profit"] = dx["value"] + dx["value_at_cost"]
                df = pd.concat([df, dx], ignore_index=True)

            df["value"] = -df["value_at_cost"]
            df = df.drop(columns=["value_at_cost"])

            all = pd.concat([df, all_x], ignore_index=True)
            all["unit_cost"] = all["unit_cost"].fillna(0)
        all["profit"] = all["profit"].fillna(0)

        # Add profits
        trades_profit = all[all["profit"] != 0].copy()
        if len(trades_profit) > 0:
            trades_profit["operation"] = "PROFIT_INVESTITII"
            trades_profit.loc[trades_profit["profit"] < 0, "type"] = (
                "PIERDERE_INVESTITII"
            )
            trades_profit["value"] = trades_profit["profit"]
            trades_profit["quantity"] = 0
            trades_profit["details"] = "profit"

            all = pd.concat(
                [
                    all,
                    trades_profit,
                ],
                ignore_index=True,
            )

        # Reconciliere cantitati
        recon = all.pivot_table(
            index=["isin"], values=["quantity"], aggfunc="sum"
        ).reset_index()
        recon = recon.merge(
            positions[["isin", "position"]],
            how="outer",
            on="isin",
            validate="one_to_one",
        ).fillna(0)
        recon["reconcile"] = recon["quantity"] - recon["position"]

        if max(recon["reconcile"]) == min(recon["reconcile"]):
            print("Reconciliere cantitati ok")
        else:
            print("Eroare reconciliere cantitati")

        # Reconciliere cash
        print("Reconciliere cash")
        for currency in all["currency"].unique():
            cash_end = cash[cash["currency"] == currency]["endingcash"].sum()
            # cash_calc = all[(all['currency']==currency) & (~all['operation'].isin(['PROFIT', 'PIERDERE']))]
            cash_calc = all[(all["currency"] == currency)]
            cash_calc = cash_calc["value"].sum()
            diff = cash_end - cash_calc

            if abs(diff) <= 0.01:
                print("Reconciliere ok", currency)
            else:
                print("Diferenta nereconciliata pe", currency, diff)
                print(round(cash_end, 2), round(cash_calc, 2))
                # print(all[(all['currency']==currency) & (all['value']!=all[currency])])

        # Detaliere contraparte
        all["partner"] = custodian_code
        all["account"] = "IBKR_USD"
        all["custodian"] = custodian_code

        all.loc[
            (all["operation"].str.startswith("VIRAMENT")) & (all["value"] >= 0),
            "operation",
        ] = "VIR_INT_IN_BROKER_VALUTA"

        all.loc[
            (all["operation"].str.startswith("VIRAMENT")) & (all["value"] < 0),
            "operation",
        ] = "VIR_INT_OUT_BROKER_VALUTA"

        # FILTRARI TEMPORARE PRE-EXPORT
        stocks = ["BIMBOA", "ECNS", "MCHI", "VNM"]
        all.loc[
            (all["symbol"].isin(stocks))
            & (
                all["operation"].isin(
                    [
                        "BUY_BOND",
                    ]
                )
            ),
            "operation",
        ] = "BUY_STOCK"

        # Filter by lock date if it exists
        if lock_date:
            all = all[all["date"] >= str(lock_date)]

        all["transactionid"] = all["transactionid"].astype(str)

        def make_transaction_ids_unique(df):
            """
            Make transaction IDs unique by adding incremental counters to duplicates.
            """
            # Create mask for duplicate transaction IDs
            is_duplicate = df.duplicated("transactionid", keep="first")

            if is_duplicate.any():
                # Get cumulative count for each group, starting at 0
                dup_counts = (
                    df[is_duplicate].groupby("transactionid").cumcount()
                )

                # Convert everything to string and concatenate
                df.loc[is_duplicate, "transactionid"] = (
                    df.loc[is_duplicate, "transactionid"].astype(str)
                    + ".*"
                    + (dup_counts + 1).astype(str)
                )

            return df

        # Apply the function to your DataFrame
        all = make_transaction_ids_unique(all)

        print(all)

        today = str(datetime.datetime.today())[:10]
        save_file = (
            settings.FILE_ROOT
            + "reports/journal_ibkr_{today}.xlsx".format(today=today)
        )
        all_export = all.copy()
        all_export["instrument"] = custodian_code + "_" + all_export["symbol"]
        all_export["id"] = None

        all_export.to_excel(save_file, index=False)
        print("Rezults saved to", save_file)

        print(all_export[all_export["operation"].str.contains("STAMP_DUTY")])

        # Upload journal to database

        # Missing operations
        vals = all["operation"].unique()
        all_vals = Operation.objects.values_list("operation_code", flat=True)
        missing = list(set(vals) - set(all_vals))
        if len(missing) > 0:
            print("Missing operations:", missing)

        # Missing instruments
        vals = all["symbol"].unique()
        all_vals = Instrument.objects.values_list("symbol", flat=True)
        print("Missing instruments:", set(vals) - set(all_vals))

        # Filtrare data
        # if 'date' in all.columns:
        #     all = all[all['date']>=str(lock_date)]

        # Prepare update
        model_instances = []
        for i, row in all.iterrows():
            model_instances += [
                Journal(
                    # currency_code = Currency.objects.get(currency_code = row['currency']),
                    ubo=Ubo.objects.get(ubo_code=row["ubo"]),
                    custodian=Custodian.objects.get(
                        custodian_code=row["custodian"]
                    ),
                    account=Account.objects.get(account_code=row["account"]),
                    operation=Operation.objects.get(
                        operation_code=row["operation"]
                    ),
                    partner=Partner.objects.get(partner_code=row["partner"]),
                    instrument=Instrument.objects.get(
                        symbol=row["symbol"],
                        custodian__custodian_code=custodian_code,
                    ),
                    date=row["date"],
                    transactionid=row["transactionid"],
                    value=row["value"],
                    quantity=row["quantity"],
                    details=row["details"],
                    # unit_cost = row['unit_cost'],
                    # profit = row['profit'],
                )
            ]

        # Upload model_instances to database
        if UPLOAD:
            unique = [
                "ubo",
                "custodian",
                "account",
                "transactionid",
            ]
            update_fields = unique + [
                "partner",
                "instrument",
                "date",
                "value",
                "quantity",
                "details",
            ]
            Journal.objects.bulk_create(
                model_instances,
                # ignore_conflicts=True,
                update_conflicts=True,
                unique_fields=unique,
                update_fields=update_fields,
            )
            print(
                "IBKR journal imported into database, max date imported is",
                max(all["date"]),
            )
        else:
            print("NOT UPLOADED - journal IBKR - set UPDATE to True to upload")
        print("All done")
