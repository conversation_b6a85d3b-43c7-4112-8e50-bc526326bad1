from django.core.management.base import BaseCommand
from django.conf import settings
import pandas as pd
from pathlib import Path


class Command(BaseCommand):
    help = 'Create a sample helper_mapping.xlsx file for the helper mapping editor'

    def add_arguments(self, parser):
        parser.add_argument(
            '--overwrite',
            action='store_true',
            help='Overwrite existing helper_mapping.xlsx file',
        )

    def handle(self, *args, **options):
        """Create the sample helper mapping Excel file"""
        
        # Define the file path
        helper_files_dir = Path(settings.BASE_DIR) / 'port' / 'helper_files'
        helper_files_dir.mkdir(exist_ok=True)
        file_path = helper_files_dir / 'helper_mapping.xlsx'
        
        # Check if file exists and overwrite flag
        if file_path.exists() and not options['overwrite']:
            self.stdout.write(
                self.style.WARNING(
                    f'File {file_path} already exists. Use --overwrite to replace it.'
                )
            )
            return
        
        try:
            # Sample data for helper_mapping sheet
            helper_mapping_data = {
                'Source_Field': ['field1', 'field2', 'field3', 'field4', 'field5'],
                'Target_Field': ['target1', 'target2', 'target3', 'target4', 'target5'],
                'Data_Type': ['string', 'integer', 'decimal', 'date', 'boolean'],
                'Required': ['Yes', 'No', 'Yes', 'No', 'Yes'],
                'Default_Value': ['', '0', '0.00', '', 'False'],
                'Description': [
                    'Primary identifier field',
                    'Numeric counter field',
                    'Monetary amount field',
                    'Transaction date field',
                    'Active status flag'
                ]
            }
            
            # Sample data for operation_mapping sheet
            operation_mapping_data = {
                'Operation_Code': ['BUY', 'SELL', 'DIV', 'INT', 'FEE'],
                'Operation_Name': ['Purchase', 'Sale', 'Dividend', 'Interest', 'Fee'],
                'Operation_Type': ['TRADE', 'TRADE', 'INCOME', 'INCOME', 'EXPENSE'],
                'Sign': ['-1', '1', '1', '1', '-1'],
                'Account_Impact': ['DEBIT', 'CREDIT', 'CREDIT', 'CREDIT', 'DEBIT'],
                'Notes': [
                    'Asset purchase transaction',
                    'Asset sale transaction',
                    'Dividend income received',
                    'Interest income received',
                    'Transaction fee charged'
                ]
            }
            
            # Sample data for currency_mapping sheet
            currency_mapping_data = {
                'Currency_Code': ['USD', 'EUR', 'GBP', 'RON', 'CHF'],
                'Currency_Name': ['US Dollar', 'Euro', 'British Pound', 'Romanian Leu', 'Swiss Franc'],
                'Symbol': ['$', '€', '£', 'lei', 'CHF'],
                'Decimal_Places': [2, 2, 2, 2, 2],
                'Is_Base_Currency': ['No', 'No', 'No', 'Yes', 'No'],
                'Exchange_Rate_Source': ['BNR', 'BNR', 'BNR', 'N/A', 'BNR']
            }
            
            # Sample data for instrument_mapping sheet
            instrument_mapping_data = {
                'Symbol': ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'AMZN'],
                'Name': ['Apple Inc.', 'Microsoft Corporation', 'Alphabet Inc.', 'Tesla Inc.', 'Amazon.com Inc.'],
                'Type': ['STOCK', 'STOCK', 'STOCK', 'STOCK', 'STOCK'],
                'Exchange': ['NASDAQ', 'NASDAQ', 'NASDAQ', 'NASDAQ', 'NASDAQ'],
                'Currency': ['USD', 'USD', 'USD', 'USD', 'USD'],
                'ISIN': ['US0378331005', 'US5949181045', 'US02079K3059', 'US88160R1014', 'US0231351067'],
                'Active': ['Yes', 'Yes', 'Yes', 'Yes', 'Yes']
            }
            
            # Create DataFrames
            helper_mapping_df = pd.DataFrame(helper_mapping_data)
            operation_mapping_df = pd.DataFrame(operation_mapping_data)
            currency_mapping_df = pd.DataFrame(currency_mapping_data)
            instrument_mapping_df = pd.DataFrame(instrument_mapping_data)
            
            # Write to Excel file with multiple sheets
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                helper_mapping_df.to_excel(writer, sheet_name='helper_mapping', index=False)
                operation_mapping_df.to_excel(writer, sheet_name='operation_mapping', index=False)
                currency_mapping_df.to_excel(writer, sheet_name='currency_mapping', index=False)
                instrument_mapping_df.to_excel(writer, sheet_name='instrument_mapping', index=False)
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully created helper_mapping.xlsx at: {file_path}'
                )
            )
            self.stdout.write(
                self.style.SUCCESS(
                    'Sheets created: helper_mapping, operation_mapping, currency_mapping, instrument_mapping'
                )
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(
                    f'Error creating helper_mapping.xlsx: {str(e)}'
                )
            )
