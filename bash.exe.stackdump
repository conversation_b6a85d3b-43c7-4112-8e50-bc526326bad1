Stack trace:
Frame         Function      Args
0007FFFFBBB0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFAAB0) msys-2.0.dll+0x1FE8E
0007FFFFBBB0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBE88) msys-2.0.dll+0x67F9
0007FFFFBBB0  000210046832 (000210286019, 0007FFFFBA68, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFBBB0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBBB0  000210068E24 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBE90  00021006A225 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFEDC540000 ntdll.dll
7FFEDB4C0000 KERNEL32.DLL
7FFED9670000 KERNELBASE.dll
7FFEDAD60000 USER32.dll
7FFED9F40000 win32u.dll
7FFEDB360000 GDI32.dll
7FFED9CB0000 gdi32full.dll
7FFEDA220000 msvcp_win.dll
7FFED9DF0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFEDC420000 advapi32.dll
7FFEDB390000 msvcrt.dll
7FFEDB2B0000 sechost.dll
7FFEDBF50000 RPCRT4.dll
7FFED8B60000 CRYPTBASE.DLL
7FFED9F70000 bcryptPrimitives.dll
7FFEDB270000 IMM32.DLL
7FFEBC200000 ctxapclient64.dll
7FFEDC090000 combase.dll
7FFEC6120000 VERSION.dll
7FFED9560000 bcrypt.dll
7FFED84E0000 ntmarta.dll
