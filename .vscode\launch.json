{"version": "0.2.0", "configurations": [{"name": "Django: Run Server", "type": "python", "request": "launch", "program": "${workspaceFolder}/manage.py", "args": ["runserver", "127.0.0.1:8000"], "django": true, "autoReload": {"enable": true}, "env": {"DJANGO_SETTINGS_MODULE": "nch.settings"}, "console": "integratedTerminal", "python": "C:/Users/<USER>/Desktop/nch_2/nch/venv/Scripts/python.exe", "cwd": "${workspaceFolder}", "stopOnEntry": false, "justMyCode": false}, {"name": "Django: Debug Current File", "type": "python", "request": "launch", "program": "${file}", "console": "integratedTerminal", "python": "C:/Users/<USER>/Desktop/nch_2/nch/venv/Scripts/python.exe", "cwd": "${workspaceFolder}", "env": {"DJANGO_SETTINGS_MODULE": "nch.settings"}, "django": true, "justMyCode": false}, {"name": "Django: Management Command", "type": "python", "request": "launch", "program": "${workspaceFolder}/manage.py", "args": ["${input:managementCommand}"], "django": true, "env": {"DJANGO_SETTINGS_MODULE": "nch.settings"}, "console": "integratedTerminal", "python": "C:/Users/<USER>/Desktop/nch_2/nch/venv/Scripts/python.exe", "cwd": "${workspaceFolder}", "justMyCode": false}, {"name": "Django: IBKR API Command", "type": "python", "request": "launch", "program": "${workspaceFolder}/manage.py", "args": ["api_ibkr"], "django": true, "env": {"DJANGO_SETTINGS_MODULE": "nch.settings"}, "console": "integratedTerminal", "python": "C:/Users/<USER>/Desktop/nch_2/nch/venv/Scripts/python.exe", "cwd": "${workspaceFolder}", "justMyCode": false}, {"name": "Django: TDV API Command", "type": "python", "request": "launch", "program": "${workspaceFolder}/manage.py", "args": ["api_tdv"], "django": true, "env": {"DJANGO_SETTINGS_MODULE": "nch.settings"}, "console": "integratedTerminal", "python": "C:/Users/<USER>/Desktop/nch_2/nch/venv/Scripts/python.exe", "cwd": "${workspaceFolder}", "justMyCode": false}, {"name": "Django: BNR Currency Command", "type": "python", "request": "launch", "program": "${workspaceFolder}/manage.py", "args": ["curs_bnr"], "django": true, "env": {"DJANGO_SETTINGS_MODULE": "nch.settings"}, "console": "integratedTerminal", "python": "C:/Users/<USER>/Desktop/nch_2/nch/venv/Scripts/python.exe", "cwd": "${workspaceFolder}", "justMyCode": false}, {"name": "Django: Upload IBKR Portfolio", "type": "python", "request": "launch", "program": "${workspaceFolder}/manage.py", "args": ["upload_ibkr_port"], "django": true, "env": {"DJANGO_SETTINGS_MODULE": "nch.settings"}, "console": "integratedTerminal", "python": "C:/Users/<USER>/Desktop/nch_2/nch/venv/Scripts/python.exe", "cwd": "${workspaceFolder}", "justMyCode": false}, {"name": "Django: Bond Accruals", "type": "python", "request": "launch", "program": "${workspaceFolder}/manage.py", "args": ["bond_accruals_ql"], "django": true, "env": {"DJANGO_SETTINGS_MODULE": "nch.settings"}, "console": "integratedTerminal", "python": "C:/Users/<USER>/Desktop/nch_2/nch/venv/Scripts/python.exe", "cwd": "${workspaceFolder}", "justMyCode": false}, {"name": "Django: Shell", "type": "python", "request": "launch", "program": "${workspaceFolder}/manage.py", "args": ["shell"], "django": true, "env": {"DJANGO_SETTINGS_MODULE": "nch.settings"}, "console": "integratedTerminal", "python": "C:/Users/<USER>/Desktop/nch_2/nch/venv/Scripts/python.exe", "cwd": "${workspaceFolder}", "justMyCode": false}, {"name": "Django: <PERSON><PERSON><PERSON>", "type": "python", "request": "launch", "program": "${workspaceFolder}/manage.py", "args": ["migrate"], "django": true, "env": {"DJANGO_SETTINGS_MODULE": "nch.settings"}, "console": "integratedTerminal", "python": "C:/Users/<USER>/Desktop/nch_2/nch/venv/Scripts/python.exe", "cwd": "${workspaceFolder}", "justMyCode": false}, {"name": "Python: Current File", "type": "python", "request": "launch", "program": "${file}", "console": "integratedTerminal", "python": "C:/Users/<USER>/Desktop/nch_2/nch/venv/Scripts/python.exe", "cwd": "${workspaceFolder}", "justMyCode": false}], "inputs": [{"id": "managementCommand", "description": "Django management command", "default": "help", "type": "promptString"}]}