from django.db import models
from django.contrib import admin
from datetime import datetime
from django.utils.timezone import make_aware
from django.utils import timezone
from simple_history.models import HistoricalRecords # For database operations tracking 


# Create your models here.

# default_date = make_aware(datetime.strptime("1900-01-01", '%Y-%m-%d'))

class Ubo(models.Model):
    ubo_code = models.CharField(max_length=100, unique=True)
    ubo_name = models.CharField(max_length=100)
    ubo_details = models.CharField(max_length=100, default='DD')

    def __str__(self):
        return self.ubo_code


class Currency(models.Model):
    currency_code = models.CharField(max_length=3, unique=True)
    currency_name = models.CharField(max_length=100)

    def __str__(self):
        return self.currency_code


class Bnr(models.Model):
    currency_code = models.ForeignKey(Currency, on_delete=models.CASCADE)
    date = models.DateField("date published")
    value = models.FloatField()
    value_exact = models.FloatField(null=True, blank=True, default=None)
    class Meta:
        unique_together = ['currency_code', 'date',]


class Partner_type(models.Model):
    partner_type_code = models.CharField(max_length=10, unique=True)
    journal_code = models.CharField(max_length=100)

    def __str__(self):
        return self.partner_type_code


class Partner(models.Model):
    partner_code = models.CharField(max_length=15, unique=True, default='IBKR')
    partner_type = models.ForeignKey(Partner_type, related_name='partner_type_id', on_delete=models.CASCADE, default=1)
    partner_name = models.CharField(max_length=100, default='')

    def __str__(self):
        return self.partner_code


class Custodian(models.Model):
    custodian_code = models.CharField(max_length=10, unique=True)
    custodian_type = models.ForeignKey(Partner_type, related_name='custodian_type_id', on_delete=models.CASCADE, default=1)
    custodian_name = models.CharField(max_length=100, default='')

    def __str__(self):
        return self.custodian_code


class Account(models.Model):
    ubo = models.ForeignKey(Ubo, related_name='account_ubo', on_delete=models.CASCADE, default=1)
    custodian = models.ForeignKey(Custodian, related_name='account_custodian', on_delete=models.CASCADE, default=1)
    custodian_detail = models.CharField(max_length=10, default='', null=True, blank=True)

    currency = models.ForeignKey(Currency, related_name='currency', on_delete=models.CASCADE, default=1)
    account_code = models.CharField(max_length=10, unique=True)
    account_name = models.CharField(max_length=100, default='')

    class Meta:
        unique_together = ('ubo', 'account_code',)

    def __str__(self):
        return self.account_code


class Accounting(models.Model):
    account_code = models.CharField(max_length=100, unique=True)
    account_name = models.CharField(max_length=100, default="")
    # analytic_structure = models.CharField(max_length=100, default='ABC')
    has_currency = models.BooleanField(default=False)
    has_custodian_debit = models.BooleanField(default=False)
    has_custodian_credit = models.BooleanField(default=False)
    
    has_partner_debit = models.BooleanField(default=False)
    has_partner_credit = models.BooleanField(default=False)
    has_symbol = models.BooleanField(default=False)
    has_dot = models.BooleanField(default=False)
    
    def __str__(self):
        return f"{self.account_code} {self.account_name}"


class Instrument(models.Model):
    currency = models.ForeignKey(Currency, on_delete=models.CASCADE, default=1)
    custodian = models.ForeignKey(Custodian, related_name='custodian', on_delete=models.CASCADE, default=1)

    symbol = models.CharField(max_length=100)
    isin = models.CharField(max_length=12)
    name = models.CharField(max_length=100)
    type = models.CharField(max_length=100)
    principal = models.FloatField(null=True, blank=True, default=None)
    face_value = models.FloatField(default=1.0)
    interest = models.FloatField(null=True, blank=True, default=None)
    depo_start = models.DateField(null=True, blank=True, default=None)
    bond_issue = models.DateField(null=True, blank=True, default=None)
    bond_first_coupon = models.DateField(null=True, blank=True, default=None)
    maturity = models.DateField(null=True, blank=True, default=None)
    convention = models.CharField(max_length=50, null=True, blank=True, default=None)
    calendar = models.CharField(max_length=50, null=True, blank=True, default=None)
    bond_coupon_count = models.IntegerField(null=True, blank=True, default=None)
    sector = models.CharField(max_length=100)
    country = models.CharField(max_length=100)

    class Meta:
        unique_together = ('custodian', 'symbol',)

    def __str__(self):
        return f"{self.custodian}_{self.symbol}"


class Document_type(models.Model):
    document_type = models.CharField(max_length=100, unique=True, default='X')
    document_name = models.CharField(max_length=100, default='')

    def __str__(self):
        return self.type


class Document(models.Model):
    document_type = models.ForeignKey(Document_type, on_delete=models.CASCADE, default=1)
    partner_code = models.ForeignKey(Partner, on_delete=models.CASCADE, default=1)
    id_doc = models.CharField(max_length=10, unique=True)
    name = models.CharField(max_length=100)
    date = models.DateField(default=timezone.now)


class Operation(models.Model):
    operation_code = models.CharField(max_length=100, unique=True)
    operation_name = models.CharField(max_length=100, default='')

    debit = models.ForeignKey(Accounting, related_name='debit', on_delete=models.CASCADE, default=1)
    credit = models.ForeignKey(Accounting, related_name='credit', on_delete=models.CASCADE, default=1)

    def __str__(self):
        return self.operation_code


class Journal(models.Model):
    ubo = models.ForeignKey(Ubo, on_delete=models.CASCADE, default=1)
    custodian = models.ForeignKey(Custodian, on_delete=models.CASCADE)
    account = models.ForeignKey(Account, on_delete=models.CASCADE)
    operation = models.ForeignKey(Operation, on_delete=models.CASCADE)
    partner = models.ForeignKey(Partner, on_delete=models.CASCADE)
    instrument = models.ForeignKey(Instrument, on_delete=models.CASCADE)
    date = models.DateField(default=timezone.now)
    transactionid = models.CharField(max_length=100)
    # value_gross = models.FloatField(default=0.0)
    value = models.FloatField() # at cost
    value_ron = models.FloatField(default=0)
    bnr = models.FloatField(default=1.0)
    quantity = models.FloatField()
    details = models.CharField(max_length=250)
    # unit_cost = models.FloatField(default=0.0)
    # profit = models.FloatField(default=0.0)
    storno = models.BooleanField(default=False)
    lock = models.BooleanField(default=False)

    # For logging 
    history = HistoricalRecords()

    class Meta:
        unique_together = ('ubo', 'custodian', 'account', 'transactionid') #, 'operation')
        
    def __str__(self):
        return f"{self.ubo}_{self.custodian}_{self.operation}_{self.date}"


class Portfolio(models.Model):
    ubo = models.ForeignKey(Ubo, on_delete=models.CASCADE, default=1)
    instrument = models.ForeignKey(Instrument, on_delete=models.CASCADE)
    date = models.DateField()
    cost = models.FloatField()
    value = models.FloatField()
    quantity = models.FloatField()
    accruedint = models.FloatField(default=0)
    class Meta:
        unique_together = ('ubo', 'instrument', 'date')


class Lock(models.Model):
    lock_date = models.DateField(unique=True)
    def __str__(self):
        return f"{self.lock_date}"
    

class Deposits(models.Model):
    CONVENTION_CHOICES = [
        ('360', '360'),
        ('365', '365'),
        ('ACT/ACT', 'ACT/ACT'),
    ]

    deposit = models.ForeignKey('Instrument', on_delete=models.CASCADE)
    principal = models.DecimalField(max_digits=15, decimal_places=2)
    interest_rate = models.FloatField()
    start = models.DateField()
    maturity = models.DateField()
    convention = models.CharField(max_length=10, choices=CONVENTION_CHOICES)
    interest_amount = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    interest_calculated = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    check_actual_vs_calc = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    new_deposit = models.BooleanField(null=True, blank=True)
    liquidated = models.BooleanField(null=True, blank=True)
    details = models.CharField(max_length=255, null=True, blank=True)

    class Meta:
        unique_together = ['deposit', 'maturity']
        verbose_name = 'Deposit'
        verbose_name_plural = 'Deposits'

    def __str__(self):
        return f"{self.deposit}_{self.maturity}"
