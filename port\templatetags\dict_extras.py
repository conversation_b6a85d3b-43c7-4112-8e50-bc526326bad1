from django import template

register = template.Library()

@register.filter
def lookup(dictionary, key):
    """
    Template filter to look up a dictionary value by key.
    Usage: {{ dict|lookup:key }}
    """
    if isinstance(dictionary, dict):
        return dictionary.get(key, '')
    return ''

@register.filter
def get_item(dictionary, key):
    """
    Alternative template filter for dictionary lookup.
    Usage: {{ dict|get_item:key }}
    """
    return dictionary.get(key, '') if isinstance(dictionary, dict) else ''
