
""" Export Morgan Stanley instruments and portfolio"""
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings 
from port.models import Instrument, Currency, Ubo, Portfolio, Partner, Custodia
class Command(BaseCommand):

    def handle(self, *args, **options):

        import glob
        import os
        import re
        import fnmatch
        import pandas as pd
        from pathlib import Path
        from datetime import datetime

        broker = 'mst'

        file_root = settings.FILE_ROOT + "{broker}/".format(broker=broker)

        # Import latest
        tabs = [
            'holdings', 
            'activity', 
            ]

        tab = {}
        
        for t in tabs:
            pattern = '*{}*.xlsx'.format(t)
            list_of_files = glob.glob(file_root + pattern) 
            latest_file = max(list_of_files, key=os.path.getctime)

            df = pd.read_excel(latest_file)
            tab[t] = df

        # Import instruments        
        secinfo =  tab['holdings']
        rename = {
            }
        cols = [ 'symbol', 'name', 'cusip']
        secinfo.columns = secinfo.columns.str.lower()
        secinfo = secinfo.rename(columns=rename)[cols]
        secinfo.loc[secinfo['symbol'].isna(), 'symbol'] = secinfo['cusip']                                      
        secinfo['currency'] = 'USD'

        secinfo['isin'] = '??' + secinfo['symbol'] +'?'
        secinfo = secinfo.drop_duplicates(keep='first')

        # Define new instruments to database
        symbols = df['symbol'].unique()
        for c in symbols:
            existing = Symbols.objects.filter(currency_code=c).all().values()
            if len(existing)==0:
                obj = Currency(
                    currency_code = c,
                    currency_name = c, 
                )
                obj.save()



        for index, row in secinfo.iterrows():
            cid = Currency.objects.get(currency=row['currency'])
            custodianid = Custodian.objects.get(custodian_code='MST')

            instrumentid = Instrument.objects.filter(symbol=row['symbol'], custodian=broker.upper())
 
            if len(instrumentid)==0:
                obj = Instrument(
                    currency=cid,
                    symbol=row['symbol'],
                    isin=row['isin'],
                    name=row['name'],
                    type='PLEASE UPDATE',
                    sector='PLEASE UPDATE',
                    country='PLEASE UPDATE',
                    custodian=custodianid,
                )
                obj.save()
            else:
                # print('existing', existing)
                pass


        # Import portfolio
        portfolio =  tab['holdings']

        rename = {
            'market value ($)': 'value',
            'adjusted cost ($)': 'cost',
            'as of': 'date',
            }
        cols = [ 'symbol', 'cusip', 'value', 'cost', 'date', 'quantity']

        portfolio.columns = portfolio.columns.str.lower()
        
        portfolio = portfolio.rename(columns=rename)[cols]
        portfolio.loc[portfolio['symbol'].isna(), 'symbol'] = portfolio['cusip']
        portfolio.loc[portfolio['date']=='-', 'date'] = max(portfolio['date'])
        portfolio.loc[portfolio['cost']=='-', 'cost'] = portfolio['value']
        portfolio.loc[portfolio['quantity']=='-', 'quantity'] = portfolio['value']
        portfolio['currency'] = 'USD'
        portfolio['ubo'] = 'DD'

        print(portfolio)               


        # Add new symbols in Currency table
        symbols = df['symbol'].unique()
        for x in symbols:
            existing = Instrument.objects.filter(custodian_code=, currency_code=x).all().values()
            if len(existing)==0:
                obj = Currency(
                    currency_code = c,
                    currency_name = c, 
                )
                obj.save()

        # Prepare update
        model_instances = [
            Bnr(
                currency_code = Currency.objects.get(currency_code = row['currency']),
                date = row['date'], 
                value = row['value'], 
            )
            for i, row in bnr.iterrows()]
        
        # Upload model_instances to database
        unique = ['ubo_code', 'instrument_id', 'date']
        update_fields = unique + ['value']
        Bnr.objects.bulk_create(
            model_instances, 
            update_conflicts=True,
            unique_fields=unique,
            update_fields=update_fields,
        )




        # Upload portfolio to database
        today = str(datetime.today())[:10]

        for index, row in portfolio.iterrows():
            instrumentid = Instrument.objects.get(symbol=row['symbol'], custodian_code=broker.upper())
            uboid = Ubo.objects.get(ubo_code=row['ubo'])

            existing = Portfolio.objects.filter(
                instrument__symbol=row['symbol'], instrument__custodian=broker.upper(), 
                ubo=uboid, date=today).all().values()

            if len(existing)==0:
                obj = Portfolio(
                    ubo_code = uboid,
                    instrument_id = instrumentid,
                    date=today,
                    cost=round(row['cost'], 2),
                    value=round(row['value'], 2),
                    quantity=round(row['quantity'], 2),
                )
                obj.save()
            else:
                obj = Portfolio(
                    id = existing[0]['id'],
                    ubo = uboid,
                    instrument = instrumentid,
                    date=today,
                    cost=round(row['cost'], 2),
                    value=round(row['value'], 2),
                    quantity=round(row['quantity'], 2),
                )
                obj.save(force_update=True)
                pass
        print(len(portfolio), 'records for {}'.format(broker))
        print('All done')


