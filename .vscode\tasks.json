{"version": "2.0.0", "tasks": [{"label": "Django: Run Server", "type": "shell", "command": "${workspaceFolder}/venv/Scripts/python.exe", "args": ["manage.py", "runserver", "127.0.0.1:8000"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}", "env": {"DJANGO_SETTINGS_MODULE": "nch.settings"}}, "problemMatcher": []}, {"label": "Django: Make Migrations", "type": "shell", "command": "${workspaceFolder}/venv/Scripts/python.exe", "args": ["manage.py", "makemigrations"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}", "env": {"DJANGO_SETTINGS_MODULE": "nch.settings"}}, "problemMatcher": []}, {"label": "Django: <PERSON><PERSON><PERSON>", "type": "shell", "command": "${workspaceFolder}/venv/Scripts/python.exe", "args": ["manage.py", "migrate"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}", "env": {"DJANGO_SETTINGS_MODULE": "nch.settings"}}, "problemMatcher": []}, {"label": "Django: Collect Static", "type": "shell", "command": "${workspaceFolder}/venv/Scripts/python.exe", "args": ["manage.py", "collectstatic", "--noinput"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}", "env": {"DJANGO_SETTINGS_MODULE": "nch.settings"}}, "problemMatcher": []}, {"label": "Django: Shell", "type": "shell", "command": "${workspaceFolder}/venv/Scripts/python.exe", "args": ["manage.py", "shell"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}", "env": {"DJANGO_SETTINGS_MODULE": "nch.settings"}}, "problemMatcher": []}, {"label": "IBKR: Download Data", "type": "shell", "command": "${workspaceFolder}/venv/Scripts/python.exe", "args": ["manage.py", "api_ibkr"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}", "env": {"DJANGO_SETTINGS_MODULE": "nch.settings"}}, "problemMatcher": []}, {"label": "TDV: Download Data", "type": "shell", "command": "${workspaceFolder}/venv/Scripts/python.exe", "args": ["manage.py", "api_tdv"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}", "env": {"DJANGO_SETTINGS_MODULE": "nch.settings"}}, "problemMatcher": []}, {"label": "BNR: Download Exchange Rates", "type": "shell", "command": "${workspaceFolder}/venv/Scripts/python.exe", "args": ["manage.py", "curs_bnr"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}", "env": {"DJANGO_SETTINGS_MODULE": "nch.settings"}}, "problemMatcher": []}, {"label": "Test: SQLite Migration", "type": "shell", "command": "${workspaceFolder}/venv/Scripts/python.exe", "args": ["test_sqlite_migration.py"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}", "env": {"DJANGO_SETTINGS_MODULE": "nch.settings"}}, "problemMatcher": []}, {"label": "Install Requirements", "type": "shell", "command": "${workspaceFolder}/venv/Scripts/pip.exe", "args": ["install", "-r", "requirements.txt"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}]}