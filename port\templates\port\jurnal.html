{% extends "base.html" %}

{% block title %}Jurnal{% endblock %}

{% load render_table from django_tables2 %}
{% load mathfilters %}

{% block content %}
    <div class="content"> 
        <table class="sortable table table-responsive">

            <thead>
                <tr>
                    <th scope="col" class="col-auto text-start" >Firma</th>
                    <th scope="col" class="col-auto text-start">Banca</th>
                    <th scope="col" class="col-auto text-start">Cont</th>
                    <th scope="col" class="col-auto text-start">Operatie</th>
                    <th scope="col" class="col-auto text-start">Contraparte</th>
                    <th scope="col" class="col-auto text-start">Instrument</th>
                    <th scope="col" class="col-auto text-end">Valoare</th>
                    <th scope="col" class="col-auto text-end">Valoare Deviza</th>
                    <th scope="col" class="col-auto text-end">Cantitate</th>
                     <!--
                    <th scope="col" class="col-auto text-end">Cost unitar</th>
                    <th scope="col" class="col-auto text-end">Profit</th>
                    -->
                    <th scope="col" class="col-auto text-end">Storno</th>

                    <th scope="col" class="col-auto text-start" >&#11035</th>


                    <th scope="col" class="col-auto text-start" >Nr.inreg.</th>
                    <th scope="col" class="col-auto text-start" >Tip inregistrare</th>
                    <th scope="col" class="col-auto text-start" >Jurnal</th>
                    <th scope="col" class="col-auto text-start">Data</th>
                    <th scope="col" class="col-auto text-start">Data scadenta</th>
                    <th scope="col" class="col-auto text-start">Numar document</th>

                    <th scope="col" class="col-auto text-start">Cod tip factura</th>

                    <th scope="col" class="col-auto text-start">Cont debit simbol</th>
                    <th scope="col" class="col-auto text-start">Cont debit titlu</th>

                    <th scope="col" class="col-auto text-start">Metoda de plata SAF-T</th>
                    <th scope="col" class="col-auto text-start">Mecanism de plata SAFT-T</th>
                    <th scope="col" class="col-auto text-start">Tip Taxa SAF_T</th>
                    <th scope="col" class="col-auto text-start">Cod TAXA SAF_T</th>

                    <th scope="col" class="col-auto text-start" >Cont credit simbol</th>
                    <th scope="col" class="col-auto text-start" >Cont credit titlu</th>

                    <th scope="col" class="col-auto text-start">Metoda de plata SAF-T</th>
                    <th scope="col" class="col-auto text-start">Mecanism de plata SAFT-T</th>
                    <th scope="col" class="col-auto text-start">Tip Taxa SAF_T</th>
                    <th scope="col" class="col-auto text-start">Cod TAXA SAF_T</th>

                    <th scope="col" class="col-auto text-start">Explicatie</th>
                    <th scope="col" class="col-auto text-end">Valoare</th>

                    <th scope="col" class="col-auto text-start">Cod Partener</th>
                    <th scope="col" class="col-auto text-start">Partener CIF</th>
                    <th scope="col" class="col-auto text-start">Partener Nume</th>
                    <th scope="col" class="col-auto text-start">Partener Rezidenta</th>
                    <th scope="col" class="col-auto text-start">Partener Judet</th>
                    <th scope="col" class="col-auto text-start">Partener Cont</th>
                    <th scope="col" class="col-auto text-start">Angajat CNP</th>
                    <th scope="col" class="col-auto text-start">Angajat Nume</th>
                    <th scope="col" class="col-auto text-start">Angajat Cont</th>
                    <th scope="col" class="col-auto text-start">Optiune TVA</th>
                    <th scope="col" class="col-auto text-start">Cota TVA</th>
                    <th scope="col" class="col-auto text-start">Cod TVA SAF-T</th>

                    <th scope="col" class="col-auto text-start">Moneda</th>
                    <th scope="col" class="col-auto text-end">Curs BNR</th>
                    <th scope="col" class="col-auto text-end">Valoare deviza</th>

                    <th scope="col" class="col-auto text-end">Stornare - Nr. inreg.</th>
                    <th scope="col" class="col-auto text-end">Incasari/plati</th>
                    <th scope="col" class="col-auto text-end">Diferente curs</th>
                    <th scope="col" class="col-auto text-end">TVA la incasare</th>
                    <th scope="col" class="col-auto text-end">Colectare/Deducere TVA</th>
                    <th scope="col" class="col-auto text-end">Efect de incasat/platit</th>
                    <th scope="col" class="col-auto text-end">Banca efect</th>
                    <th scope="col" class="col-auto text-end">Centre de cost</th>
                    <th scope="col" class="col-auto text-end">Informatii export</th>
                    <th scope="col" class="col-auto text-end">Punct de lucru</th>
                    <th scope="col" class="col-auto text-end">Deductibilitate</th>
                    <th scope="col" class="col-auto text-end">Reevaluare</th>
                    <th scope="col" class="col-auto text-end">Factura simplificata</th>
                    <th scope="col" class="col-auto text-end">Borderou de achizitie</th>
                    <th scope="col" class="col-auto text-end">Carnet prod. Agricole</th>
                    <th scope="col" class="col-auto text-end">Contract</th>
                    <th scope="col" class="col-auto text-end">Document stornat</th>

                </tr>
              </thead>


            <tbody>
                {% for x in jurnal %}

                <tr>
                                        
                    <td>{{ x.ubo__ubo_code }}</td>
                    <td>{{ x.custodian__custodian_code }}</td>
                    <td>{{ x.account__account_code }}</td>
                    <td>{{ x.operation__operation_code }}</td>
                    <td>{{ x.partner__partner_code }}</td>
                    <td>{{ x.instrument__symbol }}</td>
                    <td class="text-end" >{{ x.value_ron|floatformat:2 }}</td>
                    <td class="text-end">{{ x.value|floatformat:2 }}</td>

                    <td class="text-end">{{ x.quantity }}</td>

                    <td class="text-end">{% if x.storno %} x {% endif %}</td>
                    <td>&#11035</td>


                    <td>{{ forloop.counter }}</td>
                    <td>{{ x.custodian__custodian_type__partner_type_code }}</td>
                    <td>{{ x.custodian__custodian_type__journal_code }}</td>
                    <td>{{ x.date|date:"Ymd"  }}</td>
                    <td>{{ x.date|date:"Ymd"  }}</td>
                    <td>{{ x.transactionid }}</td>

                    <td></td>

                    <td>{{ x.debit_analitic }}</td>
                    <td>{{ x.operation__debit__account_name }}</td>
                    <td></td><td></td><td></td><td></td>
                    <td>{{ x.credit_analitic }}</td>
                    <td>{{ x.operation__credit__account_name }}</td>
                    <td></td><td></td><td></td><td></td>
                    <td>{{ x.details }}</td>
                    <td class="text-end" >{{ x.value_ron_abs|floatformat:2 }}</td>
                    <td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td>
                    <td>Neimpozabile</td>
                    <td>0.00</td>
                    <td></td>

                    <td>{{ x.instrument__currency__currency_code }}</td>
                    <td class="text-end">{{ x.bnr|floatformat:4 }}</td>
                    <td class="text-end">{{ x.value_abs|floatformat:2 }}</td>

                    <td></td> <td></td> <td></td> 
                    <td class="text-end">0</td> 
                    <td></td> <td></td> <td></td> <td></td>
                    <td>********-********-CielStd_ASV</td>
                    <td>Sediu</td>
                    <td></td> <td></td>
                    <td class="text-end">0</td> <td class="text-end">0</td> <td class="text-end">0</td> <td class="text-end">0</td> <td class="text-end">0</td>                    
                </tr>
                
                {% endfor %}

            </tbody>
        </table>

    </div>
{% endblock %}