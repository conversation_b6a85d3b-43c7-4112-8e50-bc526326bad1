from django.core.management.base import BaseCommand
from django.conf import settings
from port.models import Journal, Bnr, Instrument
import pandas as pd
from datetime import datetime
from .lib.bond import BondAccrualCalculator

class Command(BaseCommand):
    help = 'Calculate bond accruals and related operations'

    def __init__(self):
        super().__init__()
        self.calculator = BondAccrualCalculator()  # Create instance of calculator


    def handle(self, *args, **options):
        # 1. Query Journal for bond operations
        df_bonds, df_bond_info, df_bnr, df_bnr_eom = self.calculator.query_data()

        # print(df_bnr_eom[df_bnr_eom['date']>='2024-03-29'])

        # 4. Filter for specific bond
        # df_bond_info = df_bond_info[df_bond_info['symbol']=='ROMANI 5 1/2 09/18/28']
        # df_bond_info = df_bond_info[df_bond_info['symbol']=='POLAND 3 1/4 04/06/26']
        # df_bond_info = df_bond_info[df_bond_info['symbol']=='T 4 1/8 11/15/32']
        # df_bond_info = df_bond_info[df_bond_info['symbol']=='US.A3K31L']
        # df_bond_info = df_bond_info[df_bond_info['symbol']=='T 4 10/31/29']
        # df_bond_info = df_bond_info[df_bond_info['symbol']=='POLAND 3 1/4 04/06/26']
        df_bond_info = df_bond_info[df_bond_info['symbol']=='REPHUN 6 1/8 05/22/28']

        print(df_bond_info)

        df_all = pd.DataFrame()
        INDEX_COLS = ['ubo', 'custodian', 'partner', 'date', 'symbol', 'currency', 'operation', 'account', 'instrument']

        for symbol in df_bond_info['symbol'].unique():
            dx = df_bonds[df_bonds['symbol'] == symbol].copy()

            if len(dx)>0:
                print(f"\nFound {len(dx)} operations for {symbol}")
                print(dx)

                # Retrieve instrument parameters
                bond_info= df_bond_info[df_bond_info['symbol']==symbol].to_dict('records')[0]
                print('bond_info:\n', pd.DataFrame(bond_info, index=[0]))

                # Multiply quantity by face value
                dx['quantity'] = dx['quantity'] * bond_info['face_value']

                # Concatenate by date and operation
                dx = dx.pivot_table(
                    index=INDEX_COLS, 
                    columns=[ ], 
                    values=['value', 'quantity', 'details'],
                    aggfunc={
                        'value': 'sum',
                        'quantity': 'sum',
                        'details': lambda x: ','.join(list(set(x)))
                    }).reset_index()

                # Set top priority for hard data except for BOND_COUPON_RECEIVED_BROKER
                dx['priority'] = 1
                dx.loc[dx['operation'] == 'BOND_COUPON_RECEIVED_BROKER', 'priority'] = 10

                # Find all accrual dates
                date_range = pd.date_range(
                    start=dx['date'].min(),
                    end=min(datetime.today().date(), bond_info['maturity']),
                    freq='ME'
                )
                last_trading_days = [self.calculator.get_last_trading_day(date).date() for date in date_range]
                print(last_trading_days)

                # Prepare blank dataframe with accrual dates 
                all_accrual_dates = pd.DataFrame({
                    'operation': 'INTEREST_ACCRUAL_BOND',
                    'date': last_trading_days,
                    'end_of_month': True,
                    'details': f'Montly accrual {symbol}',
                    'quantity': 0,
                    'priority': 4,
                }).drop_duplicates()

                # Move all_accrual_dates dates to actual end of the month
                all_accrual_dates['date'] = pd.to_datetime(all_accrual_dates["date"].astype(str), format='mixed')  # Handle mixed formats
                all_accrual_dates['date'] = pd.to_datetime(all_accrual_dates['date'].dt.date) + pd.offsets.MonthEnd(0)
                last_trading_days = [x.date() for x in all_accrual_dates['date'].unique()]

                # Add transaction dates to accrual dates 
                other_dates_list = sorted(list(set(dx['date'].to_list()) - set(last_trading_days))) # Make sure lists are either timestamp or date format, not mixed

                other_dates_accrued = pd.DataFrame({
                    'operation': 'INTEREST_ACCRUAL_BOND',
                    'date': other_dates_list,
                    'end_of_month': False,
                    'details': f'Spot accrual {symbol}',
                    'quantity': 0,
                    'priority': 3,
                }).drop_duplicates()

                # all_accrual_dates = pd.concat([all_accrual_dates, other_dates_accrued, other_dates_settled], ignore_index=True)
                all_accrual_dates = pd.concat([all_accrual_dates, other_dates_accrued], ignore_index=True)

                # Merge accrual dates into main dataframe
                dx = pd.concat([dx, all_accrual_dates], join='outer', ignore_index=True)
                dx.loc[dx['end_of_month'].isna(), 'end_of_month'] = False

                # Fill missing info
                dx[INDEX_COLS] = dx[INDEX_COLS].ffill()

                # Sort table to prepare calculation of cumulative sums
                dx["date"] = pd.to_datetime(dx["date"])
                dx.sort_values(by=['date', 'priority'], inplace=True)

                # Cumulate quantities
                dx['quantity_total'] = dx['quantity'].fillna(0).cumsum()

                # Add settlement dates
                dx['settlement'] = dx['date'].apply(lambda x, y=bond_info['currency']: self.calculator.get_settlement_date(x, y))

                # Leave coupons with same-day settlement
                dx.loc[dx['operation'].str.contains('BOND_COUPON_RECEIVED'), 'settlement'] = dx['date']

                # Correct settlment dates for coupon payments
                sameday_operations = ['BOND_COUPON_RECEIVED_BROKER', 'BOND_ACCRUAL_REVERSAL','BOND_COUPON_RECEIVED_BANCA' ]
                sameday_exclusions = ['SELL_BOND_BANCA', 'SELL_BOND_BROKER', 'BUY_BOND_BROKER', 'BUY_BOND_BANCA']
                sameday_settlements = dx[dx['operation'].isin(sameday_operations)]['date'].unique()
                dx.loc[
                    (dx['date'].isin(sameday_settlements))
                    & (~dx['operation'].isin(sameday_exclusions))
                    , 'settlement'] = dx['date']

                # Calculate accrual days
                schedule = self.calculator.get_coupon_schedule(
                    bond_issue = bond_info['bond_issue'],
                    bond_first_coupon = bond_info['bond_first_coupon'],
                    maturity = bond_info['maturity'],
                    bond_coupon_count = bond_info['bond_coupon_count'],
                )
                schedule['date'] = pd.to_datetime(schedule['last_coupon_date'])
                schedule.drop(columns=['coupon_number'], inplace=True)

                # Merge coupon dates
                dx["date"] = pd.to_datetime(dx["date"])
                dx = pd.merge_asof(dx, schedule, on='date', allow_exact_matches=False)
                dx['date'] = pd.to_datetime(dx['date'])
                dx['last_coupon_date'] = pd.to_datetime(dx['last_coupon_date'])
                dx['next_coupon_date'] = pd.to_datetime(dx['next_coupon_date'])
                dx['settlement'] = pd.to_datetime(dx['settlement'])

                # Accrual days
                dx['accrual_days'] = (dx['settlement'] - dx['last_coupon_date']).dt.days

                # Interest divider
                dx['interest_divider'] =  (dx['next_coupon_date'] -  dx['last_coupon_date']).dt.days

                # Accrued interest, cumulated
                dx['acc_interest_total'] = 0.0
                dx.loc[dx['operation'].isin([
                    'INTEREST_ACCRUAL_BOND', 'BOND_COUPON_RECEIVED_BROKER',
                    ]), 'acc_interest_total'] = dx['quantity_total'] * bond_info['interest']/100 / bond_info['bond_coupon_count'] * dx['accrual_days'] /  dx['interest_divider'] 
                

                # Cumulate settled interest by date and add to INTEREST_ACCRUAL_BOND
                op_updated = 'INTEREST_ACCRUAL_BOND'
                ops_settled = ['BOND_INTEREST_PAID_BROKER', 'BOND_INTEREST_RECEIVED_BROKER', ]
                dx['accrual_settled'] = 0.0
                dst = dx[dx['operation'].isin(ops_settled)].groupby('date')['value'].agg('sum').to_dict()
                dx.loc[dx['operation']==op_updated, 'accrual_settled'] = dx[dx['operation']==op_updated]['date'].map(dst)
                dx['accrual_settled'] = dx['accrual_settled'].fillna(0)

                # Settle coupons
                dx['coupon_settled'] = 0.0
                dx.loc[dx['operation']=='BOND_COUPON_RECEIVED_BROKER', 'coupon_settled'] = -dx['value']
                dx.loc[dx['operation']=='BOND_COUPON_RECEIVED_BROKER', 'acc_interest_total'] = dx['acc_interest_total'] -dx['value']
                

                dx.sort_values(by=['date', 'priority'], inplace=True)

                
                # Calculate incremental 
                dac = dx[dx['operation'].isin(['INTEREST_ACCRUAL_BOND', 'BOND_COUPON_RECEIVED_BROKER'])].copy()
                dac['accrual_total_shift'] = dac['acc_interest_total'].shift(1).fillna(0)
                dac['accrual_incremental'] = dac['acc_interest_total'] - dac['accrual_total_shift'] - dac['coupon_settled']
                dac['accrual'] = dac['accrual_incremental']+ dac['accrual_settled']
                dac = dac[['date', 'operation', 'accrual_incremental', 'accrual']]

                dx = pd.merge(dx, dac, how='outer', on=['date', 'operation'])
                dx[['accrual', 'accrual_incremental']] = dx[['accrual', 'accrual_incremental']].fillna(0)

                # Port accruals to value
                dx.loc[dx['operation']=='INTEREST_ACCRUAL_BOND', 'value'] = dx['accrual']

                # Add BNR
                dx = pd.merge_asof(
                    left=dx, right=df_bnr, on='date', by=['currency'],
                    allow_exact_matches=False, direction='backward',
                    )

                # Add BNR end of month
                dx = pd.merge_asof(
                    left=dx, right=df_bnr_eom, on='date', by=['currency'],
                    allow_exact_matches=True, direction='backward',
                    )

                 # RON value
                dx['value_ron'] = (dx['value'] * dx['bnr'])
                dx['accrual_incremental_ron'] = ((dx['accrual_incremental']) * dx['bnr'])


                dx['cumsum_accrual_ron'] = dx['accrual_incremental_ron'].cumsum()

                
                
                # FX diff calc - select records
                dx.sort_values(by=['date', 'priority'], inplace=True)
                fx_days = [str(x)[:10] for x in last_trading_days]
                dfx = dx[dx['operation'].isin(['INTEREST_ACCRUAL_BOND', 'BOND_COUPON_RECEIVED_BROKER'])].copy()
                dfx['date'] = dfx['date'].dt.strftime('%Y-%m-%d')
                dfx.loc[dfx['operation']=='BOND_COUPON_RECEIVED_BROKER', 'accrual_incremental'] = dfx['coupon_settled']
                dfx['accrual_incremental_ron'] = ((dfx['accrual_incremental']) * dx['bnr'])
                dfx['cumsum_accrual_ron'] = dfx['accrual_incremental_ron'].cumsum()

                # Drop intermediary BOND_COUPON_RECEIVED_BROKER and dates inside month
                dfx = dfx[dfx['operation']!='BOND_COUPON_RECEIVED_BROKER']
                dfx = dfx[dfx['date'].isin(fx_days)]

                # FX diff calc - actual calculations
                dfx['cumsum_accrual'] = dfx['accrual_incremental'].cumsum()
                dfx['cumsum_accrual_ron_calc'] = (dfx['acc_interest_total'] * dfx['bnr_eom'])
                dfx['fx_diff_cumulated'] = dfx['cumsum_accrual_ron_calc'] - dfx['cumsum_accrual_ron']

                
                dfx['fx_diff_cumulated_shift'] = dfx['fx_diff_cumulated'].shift(1).fillna(0)
                dfx['fx_diff'] = dfx['fx_diff_cumulated'] - dfx['fx_diff_cumulated_shift']

                dfx['operation'] = 'FX_DIFF_ACCRUAL_PLUS'
                dfx.loc[dfx['fx_diff']<0, 'operation'] = 'FX_DIFF_ACCRUAL_MINUS'
                dfx['value'] = dfx['fx_diff']

                # Set instrument for FX_DIFF_ACCRUAL to RON
                dfx['instrument'] = dfx['custodian'] + '_RON'
                dfx['value_ron']  = dfx['value'] 


                drop_print = ['symbol', 'details', 'account', 'ubo', 'custodian', 'partner', 'currency', 'priority', 'end_of_month',
                'last_coupon_date', 'next_coupon_date', 'quantity', 'settlement', ]
                print('\ndfx\n', dfx.drop(columns=drop_print))

                
                # Merge dfx into dx and sort
                dx = pd.concat([dx, dfx], ignore_index=True)
                dx["date"] = pd.to_datetime(dx["date"])
                dx.sort_values(by=['date', 'priority'], inplace=True, ignore_index=True)

                # Round
                round_cols = [
                    'value', 'fx_diff', 
                    'accrual_incremental', 'accrual', 'value_ron', 'accrual_incremental_ron', 'cumsum_accrual_ron', 'cumsum_accrual', 
                    'cumsum_accrual_ron_calc', 'fx_diff_cumulated', 'fx_diff_cumulated_shift', 
                    'acc_interest_total', 
                    ]
                dx[round_cols] = dx[round_cols].round(2)

                # Final formatting
                dx['date'] = dx['date'].dt.strftime('%Y-%m-%d')
                dx['id'] = None

                print('\nFinal dx\n', dx.drop(columns=drop_print))
                

                df_all = pd.concat([df_all, dx], ignore_index=True)
            
            else:
                print(f"No operations found for {symbol}")

        # Save results 
        today = str(datetime.today())[:10]
        save_path = settings.FILE_ROOT + f"reports/bond_accruals_{today}.xlsx"

        # Format export
        export_cols = ['id', 'ubo', 'custodian', 'partner', 'account', 'operation', 'instrument', 'date', 'value', 'symbol', 
            'bnr', 'value_ron', 'quantity', ]
        df_all_export = df_all[export_cols].copy()
        df_all_export['details'] = df_all_export['operation'] + ' ' + df_all_export['symbol']

        df_all_export['row_number'] = df_all_export.reset_index().index
        
        df_all_export['transactionid'] = df_all_export['operation'].astype(str)  + '_' + df_all_export['date'].astype(str) + '_' + df_all_export['row_number'].astype(str)

        df_all_export = df_all_export[df_all_export['operation'].isin(
            ['INTEREST_ACCRUAL_BOND', 
            'FX_DIFF_ACCRUAL_MINUS', 'FX_DIFF_ACCRUAL_PLUS',]
        )]
        
        
        with pd.ExcelWriter(save_path) as writer:
            df_bonds.to_excel(writer, sheet_name='All_Bonds', index=False)
            df_bnr.to_excel(writer, sheet_name='BNR_Rates', index=False)
            df_bnr_eom.to_excel(writer, sheet_name='BNR_ENd_Month', index=False)
            df_bond_info.to_excel(writer, sheet_name='Bond_Definitions', index=False)
            df_all.to_excel(writer, sheet_name='CALCULE', index=False)
            df_all_export.to_excel(writer, sheet_name='EXPORT', index=False)
        
        print(f"\nResults saved to {save_path}")