"""Export IBKR instruments and portfolio"""

from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from port.models import Instrument, Currency, Ubo, Portfolio, Partner, Custodian


class Command(BaseCommand):

    def handle(self, *args, **options):

        import glob
        import os
        import pandas as pd

        broker = "ibkr"

        UPDATE = True

        root = settings.FILE_ROOT + "{broker}/daily/".format(broker=broker)

        # IBKR Import relevant tables
        tabs = ["OpenPositions", "CashReport", "SecuritiesInfo"]

        tab = {}

        for t in tabs:
            list_of_files = glob.glob(root + t + "*")
            latest_file = max(list_of_files, key=os.path.getctime)
            df = pd.read_csv(latest_file).dropna(axis=1, how="all")
            tab[t] = df

            print(latest_file)

        # Import instruments
        rename = {
            "description": "name",
            "assetcategory": "type",
            "subcategory": "sector",
            "issuercountrycode": "country",
        }
        cols = [
            "currency",
            "symbol",
            "isin",
            "description",
            "assetcategory",
            "subcategory",
            "issuercountrycode",
        ]
        secinfo = tab["SecuritiesInfo"][cols].rename(columns=rename)
        secinfo = secinfo.drop_duplicates(keep="first")

        if len(secinfo) > 0:

            # Missing instruments
            vals = secinfo["symbol"].unique()
            all_vals = Instrument.objects.values_list("symbol", flat=True)
            missing = set(vals) - set(all_vals)
            secinfo = secinfo[secinfo["symbol"].isin(missing)].copy()
            if len(missing) > 0:
                print("Missing instruments:", missing)
                print(secinfo)

                # Upload missing instruments to database
                model_instances = [
                    Instrument(
                        currency=Currency.objects.get(
                            currency_code=row["currency"]
                        ),
                        custodian=Custodian.objects.get(custodian_code="IBKR"),
                        symbol=row["symbol"],
                        isin=row["isin"],
                        name=row["name"],
                        type=row["type"],
                        sector=row["sector"],
                        country=row["country"],
                    )
                    for i, row in secinfo.iterrows()
                ]
                # Upload model_instances to database
                unique = [
                    "custodian",
                    "symbol",
                ]
                update_fields = unique + [
                    "currency",
                    "isin",
                    "name",
                    "type",
                    "sector",
                    "country",
                ]
                Instrument.objects.bulk_create(
                    model_instances,
                    update_conflicts=True,
                    unique_fields=unique,
                    update_fields=update_fields,
                )

        # Import IBKR portfolio
        cols = [
            "accountid",
            "symbol",
            "reportdate",
            "costbasismoney",
            "positionvalue",
            "position",
            "accruedint",
            "currency",
        ]
        rename = {
            "accountid": "ubo",
            "reportdate": "date",
            "fromdate": "date",
            "costbasismoney": "cost",
            "positionvalue": "value",
            "position": "quantity",
            "slbnetcash": "value",
            "accruedint": "accruedint",
            "brokerinterestmtd": "accruedint",
        }
        ubo = {
            "U13195074": "DD",
        }

        portfolio = tab["OpenPositions"][cols].rename(columns=rename).copy()
        print(portfolio)

        # DUPLICATED
        dupli = portfolio[
            portfolio.duplicated(subset=["ubo", "symbol", "date"], keep=False)
        ]
        if len(dupli) > 0:
            print("dupli\n", dupli.sort_values(by=["date", "symbol"]))

        # Add the cash
        cols = [
            "accountid",
            "currency",
            "fromdate",
            "slbnetcash",
            "brokerinterestmtd",
        ]
        cash = tab["CashReport"][cols].rename(columns=rename)
        cash["symbol"] = cash["currency"]
        cash = cash[cash["symbol"] != "BASE_SUMMARY"]
        cash["quantity"] = cash["value"]
        cash["cost"] = cash["value"]

        print(cash)

        # Concatenate cash into portfolio
        portfolio = pd.concat([portfolio, cash])
        portfolio["ubo"] = portfolio["ubo"].map(ubo)
        print(portfolio)

        # DUPLICATED
        dupli = portfolio[
            portfolio.duplicated(subset=["ubo", "symbol", "date"], keep=False)
        ]
        if len(dupli) > 0:
            print("dupli\n", dupli.sort_values(by=["date", "symbol"]))

        # Missing instruments
        vals = portfolio["symbol"].unique()
        all_vals = (
            Instrument.objects.filter(custodian__custodian_code="IBKR")
            .all()
            .values_list("symbol", flat=True)
        )

        missing = set(vals) - set(all_vals)
        secinfo = secinfo[secinfo["symbol"].isin(missing)].copy()
        if len(missing) > 0:
            print("Missing instruments:", missing)
        portfolio["accruedint"] = portfolio["accruedint"].fillna(0.0)
        # Upload portfolio to database
        print(portfolio)
        model_instances = [
            Portfolio(
                ubo=Ubo.objects.get(ubo_code=row["ubo"]),
                instrument=Instrument.objects.get(
                    symbol=row["symbol"], custodian__custodian_code="IBKR"
                ),
                date=row["date"],
                cost=round(row["cost"], 2),
                value=round(row["value"], 2),
                quantity=round(row["quantity"], 2),
                accruedint=row["accruedint"],
            )
            for i, row in portfolio.iterrows()
        ]
        # Upload model_instances to database
        unique = ["ubo", "instrument", "date"]
        update_fields = unique + [
            "cost",
            "value",
            "quantity",
            "accruedint",
        ]
        Portfolio.objects.bulk_create(
            model_instances,
            update_conflicts=True,
            unique_fields=unique,
            update_fields=update_fields,
        )

        print(
            "IBKR imported into database, max date imported is",
            max(portfolio["date"]),
        )
